package com.wrapper.proxyapplication.Util;
import java.lang.Object;
import android.os.Process;
import java.lang.StringBuilder;
import java.lang.String;
import java.util.zip.ZipFile;
import java.io.File;
import java.util.zip.ZipEntry;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.FileInputStream;
import java.lang.Exception;
import android.content.Context;
import java.io.RandomAccessFile;
import java.nio.channels.FileChannel;
import java.nio.channels.FileLock;
import java.lang.System;
import java.io.BufferedOutputStream;
import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.zip.CRC32;
import java.util.zip.CheckedInputStream;
import java.util.zip.Checksum;
import java.io.FileNotFoundException;

public class Util extends Object	// class@0000b8
{
    public static String CPUABI;
    static final int ERROR_EXCEPTION;
    static final int ERROR_FALSE;
    static final int ERROR_FILE_EXIST;
    static final int ERROR_FILE_NOT_FOUND;
    static final int ERROR_FILE_NOT_FOUND_INZIP;
    static final int ERROR_SUCCESS;
    public static int MAX_DEX_NUM;
    public static String TAG;
    public static String dexname;
    public static boolean ifoverwrite;
    public static String libname;
    public static String securename0;
    public static String securename1;
    public static String securename11;
    public static String securename14;
    public static String securename15;
    public static String securename2;
    public static String securename3;
    public static String securename4;
    public static String securename5;
    public static String securename6;
    public static String securename7;
    public static String securename8;
    public static String securename9;
    public static String simplelibname;
    public static String versionname;

    static {	
       Util.MAX_DEX_NUM = 300;
       Util.TAG = "Util";
       Util.CPUABI = null;
       String str = "";
       Util.libname = str;
       Util.simplelibname = str;
       Util.securename0 = "00O000ll111l.dex";
       Util.securename1 = "00O000ll111l.jar";
       Util.securename2 = "000O00ll111l.dex";
       Util.securename3 = "0000000lllll.dex";
       Util.securename4 = "000000olllll.dex";
       Util.securename5 = "0OO00l111l1l";
       Util.securename6 = "o0oooOO0ooOo.dat";
       Util.securename7 = "exportService.txt";
       Util.securename8 = ".flag00O000ll111l.dex";
       Util.securename9 = ".updateIV.dat";
       Util.versionname = "tosversion";
       Util.securename11 = ".flag00O000ll111l.vdex";
       Util.securename14 = "00O000ll111l.vdex";
       Util.securename15 = "00O000ll111l.odex";
       Util.dexname = "classes.dex";
       Util.ifoverwrite = true;
       int tid = Process.myTid();
       Util.CPUABI = Util.getelffilearch(new StringBuilder()+"/proc/"+tid+"/exe");
       String cPUABI = Util.CPUABI;
       String str1 = ".so";
       String str2 = "lib";
       if (cPUABI != "86" && cPUABI != "86_64") {	
          Util.simplelibname = "shell-super.com.btw.shenmou";
          Util.libname = new StringBuilder()+str2+Util.simplelibname+str1;
       }else {	
          Util.simplelibname = "shellx-super.com.btw.shenmou";
          Util.libname = new StringBuilder()+str2+Util.simplelibname+str1;
       }	
    }
    public void Util(){	
       super();
    }
    public static int Comparetxtinzip(ZipFile apkzf,String filepathinzip,File fileinfiledir){	
       ZipEntry cookie_entry;
       int vi = 0;
       int vi1 = 0;
       if ((cookie_entry = apkzf.getEntry(filepathinzip)) == null) {	
          if (vi) {	
             try{	
                vi.close();
                if (vi1) {	
                   try{	
                      vi1.close();
                   }catch(java.io.IOException e5){	
                      e5.printStackTrace();
                      return -2;
                   }	
                }	
             }catch(java.io.IOException e5){	
                e5.printStackTrace();
                if (vi1) {	
                   try{	
                      vi1.close();
                   }catch(java.io.IOException e6){	
                      e6.printStackTrace();
                      return -2;
                   }	
                }	
                return -2;
             }catch(Exception e5){	
                if (vi1) {	
                   try{	
                      vi1.close();
                   }catch(java.io.IOException e5){	
                      e5.printStackTrace();
                      return -2;
                   }	
                }	
                throw e5;
             }	
          }	
          return -3;
       }else {	
          int vi2 = 1024;
          try{	
             byte[] checkzipbuf = new byte[vi2];
             byte[] checkfilebuf = new byte[checkfilebuf];
             vi = new BufferedInputStream(apkzf.getInputStream(cookie_entry));
             int readziplen = vi.read(checkzipbuf);
             String tmpzipstr = tmpzipstr.substring(0, readziplen);
             vi1 = new BufferedInputStream(new FileInputStream(fileinfiledir));
             int readfilelen = vi1.read(checkfilebuf);
             String tmpfilestr = new String(checkfilebuf).substring(tmpfilestr, readfilelen);
             int result = (tmpfilestr.equals(tmpzipstr))? 1: 0;	
             try{	
                vi.close();
                vi1.close();
                return result;
             }catch(java.io.IOException e5){	
                e5.printStackTrace();
                try{	
                   vi1.close();
                   return -2;
                }catch(java.io.IOException e6){	
                   e6.printStackTrace();
                   return -2;
                }	
             }catch(Exception e5){	
                try{	
                   vi1.close();
                   throw e5;
                }catch(java.io.IOException e5){	
                   e5.printStackTrace();
                   return -2;
                }	
             }catch(java.io.IOException e5){	
                e5.printStackTrace();
                return -2;
             }	
          }catch(java.lang.Exception e5){	
             try{	
                e5.printStackTrace();
                if (vi) {	
                   try{	
                      vi.close();
                      if (vi1) {	
                         try{	
                            vi1.close();
                         }catch(java.io.IOException e6){	
                            e6.printStackTrace();
                            return -2;
                         }	
                      }	
                   }catch(java.io.IOException e6){	
                      e6.printStackTrace();
                      if (vi1) {	
                         try{	
                            vi1.close();
                         }catch(java.io.IOException e7){	
                            e7.printStackTrace();
                            return -2;
                         }	
                      }	
                      return -2;
                   }catch(Exception e6){	
                      if (vi1) {	
                         try{	
                            vi1.close();
                         }catch(java.io.IOException e6){	
                            e6.printStackTrace();
                            return -2;
                         }	
                      }	
                      throw e6;
                   }	
                }	
                return -2;
             }catch(Exception e5){	
                if (vi) {	
                   try{	
                      vi.close();
                      if (vi1) {	
                         try{	
                            vi1.close();
                         }catch(java.io.IOException e5){	
                            e5.printStackTrace();
                            return -2;
                         }	
                      }	
                   }catch(java.io.IOException e5){	
                      e5.printStackTrace();
                      if (vi1) {	
                         try{	
                            vi1.close();
                         }catch(java.io.IOException e6){	
                            e6.printStackTrace();
                            return -2;
                         }	
                      }	
                      return -2;
                   }catch(Exception e5){	
                      if (vi1) {	
                         vi1.close();
                      }	
                      throw e5;
                   }	
                }	
                throw e5;
             }	
          }catch(Exception e5){	
          }	
       }	
    }
    public static String CreatenewFileName(String Oldfilename,String SplitString,String InsertString){	
       int index;
       if ((index = Oldfilename.lastIndexOf(SplitString)) >= 0) {	
          return new StringBuilder()+Oldfilename.substring(0, index)+InsertString+Oldfilename.substring(index, Oldfilename.length());
       }	
       return null;
    }
    public static int DeleteFile(String filepath){	
       File tmpfile = new File(filepath);
       if (!tmpfile.exists()) {	
          return -1;
       }	
       if (!tmpfile.delete()) {	
          return -2;
       }	
       return 1;
    }
    public static int PrepareSecurefiles(Context ctx,ZipFile apkzf){	
       String str2;
       ZipFile zipFile1;
       int vi2;
       int vi3;
       ZipEntry fileUnzip;
       int vi4;
       int vi5;
       int vi6;
       ZipEntry fileUnzip1;
       ZipEntry fileUnzip2;
       long fileUnzip3;
       int vi10;
       ZipEntry fileUnzip4;
       long fileUnzip5;
       int vi12;
       StringBuilder sappend;
       String sstr;
       int vi14;
       ZipEntry fileUnzip6;
       ZipEntry fileUnzip7;
       int vi15;
       int vi16;
       int fileUnzip8;
       int vi17;
       int vi18;
       int vi19;
       int vi20;
       int vi21;
       String str3;
       String str4;
       StringBuilder sappend2;
       String securename0;
       int vi22;
       StringBuilder sappend3;
       int vi23;
       StringBuilder Cookiefileinzip1;
       int vi24;
       int vi25;
       StringBuilder stringBuilde;
       StringBuilder firstloadfilepath1;
       StringBuilder sappend4;
       int file_count3;
       StringBuilder stringBuilde1;
       StringBuilder deleteodexresult;
       int Cookiefilepath3;
       int vi26;
       int vi27;
       ZipFile zipFile = apkzf;
       int vi = 0;
       String Appfiledir = Appfiledir.toString();
       File Appprofiledir = new File(Appfiledir);
       if (!Appprofiledir.isDirectory()) {	
          Appprofiledir.mkdir();
       }	
       String str = "/";
       String Cookiefilepath = Cookiefilepath.toString();
       String backupfilepath = backupfilepath.toString();
       String firstloadfilepath = firstloadfilepath.toString();
       String str1 = "assets/";
       String Cookiefileinzip = Cookiefileinzip.toString();
       String Libnameinapk = Libnameinapk.toString();
       try{	
          int vi1 = vi;
          try{	
             RandomAccessFile randomAccess = new RandomAccessFile(Cookiefilepath, "rw");
             try{	
                int vi8 = randomAccess;
                FileChannel fChannel = vi8.getChannel();
                try{	
                   int vi9 = fChannel;
                   FileLock flock = vi9.lock();
                   try{	
                      int vi7 = flock;
                      try{	
                         File Cookiefile = new File(Cookiefilepath);
                         if (Cookiefile.length()) {	
                            try{	
                               vi = Util.Comparetxtinzip(zipFile, Cookiefileinzip, Cookiefile);
                               if (vi == 1) {	
                                  try{	
                                     sappend = new StringBuilder().append(str1);
                                     try{	
                                        int vi13 = Appprofiledir;
                                        sstr = sappend.append(Util.securename5).toString();
                                        try{	
                                           vi12 = firstloadfilepath;
                                           StringBuilder sappend1 = new StringBuilder().append(Appfiledir).append(str);
                                           try{	
                                              vi14 = backupfilepath;
                                              backupfilepath = Util.securename5;
                                              if (Util.checkCopiedFileCrc(zipFile, sstr, new File(sappend1.append(backupfilepath).toString()))) {	
                                                 if (Util.checkCopiedFileCrc(zipFile, new StringBuilder().append(str1).append(Util.securename6).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename6).toString()))) {	
                                                    if ((fileUnzip6 = zipFile.getEntry(fileUnzip)) != null) {	
                                                       String Cookiefilepath1 = Util.libname;
                                                       vi10 = Cookiefilepath;
                                                       if (!Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Cookiefilepath).toString(), fileUnzip.getSize())) {	
                                                          backupfilepath = Util.libname;
                                                          File Cookiefilepath2 = new File(new StringBuilder().append(Appfiledir).append(str).append(fileUnzip).toString());
                                                          Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Libnameinapk).toString(), Cookiefilepath);
                                                       }else {	
                                                          vi18 = fileUnzip;
                                                       }	
                                                    }else {	
                                                       vi10 = Cookiefilepath;
                                                       vi18 = fileUnzip;
                                                    }	
                                                    if ((fileUnzip7 = zipFile.getEntry(fileUnzip)) != null) {	
                                                       if (!Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Util.securename6).toString(), fileUnzip.getSize())) {	
                                                          sstr = Util.securename6;
                                                          Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Util.securename6).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(fileUnzip).toString()));
                                                       }else {	
                                                          vi18 = fileUnzip;
                                                       }	
                                                    }else {	
                                                       vi18 = fileUnzip;
                                                    }	
                                                    if ((fileUnzip7 = zipFile.getEntry(fileUnzip)) != null) {	
                                                       backupfilepath = Util.securename7;
                                                       if (!Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Cookiefilepath).toString(), fileUnzip.getSize())) {	
                                                          backupfilepath = Util.securename7;
                                                          Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Cookiefilepath).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString()));
                                                       }	
                                                    }	
                                                    if ((fileUnzip7 = zipFile.getEntry(Cookiefileinzip)) != null) {	
                                                       backupfilepath = vi10;
                                                       if (!Util.isFileValid(Cookiefilepath, fileUnzip.getSize())) {	
                                                          Util.UnzipFile(zipFile, Cookiefileinzip, new File(Cookiefilepath));
                                                       }	
                                                    }else {	
                                                       vi17 = vi10;
                                                    }	
                                                    if (vi7) {	
                                                       try{	
                                                          vi7.release();
                                                          if (vi9) {	
                                                             try{	
                                                                vi9.close();
                                                                vi8.close();
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                try{	
                                                                   vi8.close();
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   return -1;
                                                                }	
                                                             }catch(Exception e0){	
                                                                vi15 = e0;
                                                                try{	
                                                                   vi8.close();
                                                                   throw vi15;
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   return -1;
                                                                }	
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                return -1;
                                                             }	
                                                          }	
                                                       }catch(java.io.IOException e0){	
                                                          vi15 = e0;
                                                          try{	
                                                             vi15.printStackTrace();
                                                             if (vi9) {	
                                                                try{	
                                                                   vi9.close();
                                                                   vi8.close();
                                                                   vi16 = -1;
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   try{	
                                                                      vi8.close();
                                                                      return -1;
                                                                   }catch(java.io.IOException e0){	
                                                                      e0.printStackTrace();
                                                                      return -1;
                                                                   }	
                                                                }catch(Exception e0){	
                                                                   int vi11 = e0;
                                                                   try{	
                                                                      vi8.close();
                                                                      throw vi11;
                                                                   }catch(java.io.IOException e0){	
                                                                      e0.printStackTrace();
                                                                      return -1;
                                                                   }	
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   return -1;
                                                                }	
                                                             }else {	
                                                                vi16 = -1;
                                                             }	
                                                             return vi16;
                                                          }catch(Exception e0){	
                                                             try{	
                                                                vi15 = e0;
                                                                if (vi9) {	
                                                                   vi9.close();
                                                                   try{	
                                                                      vi8.close();
                                                                   }catch(java.io.IOException e0){	
                                                                      e0.printStackTrace();
                                                                      return -1;
                                                                   }	
                                                                }	
                                                                throw vi15;
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                try{	
                                                                   vi8.close();
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   return -1;
                                                                }	
                                                             }catch(Exception e0){	
                                                                vi15 = e0;
                                                                try{	
                                                                   vi8.close();
                                                                   throw vi15;
                                                                }catch(java.io.IOException e0){	
                                                                   e0.printStackTrace();
                                                                   return -1;
                                                                }	
                                                             }	
                                                             return -1;
                                                          }	
                                                       }catch(Exception e0){	
                                                       }	
                                                    }	
                                                    fileUnzip8 = 2;
                                                    return fileUnzip;
                                                 }else {	
                                                    vi17 = Cookiefilepath;
                                                 }	
                                              }else {	
                                                 vi17 = Cookiefilepath;
                                              }	
                                           }catch(java.lang.Exception e0){	
                                              zipFile1 = zipFile;
                                              firstloadfilepath = Cookiefileinzip;
                                              str2 = str1;
                                              vi2 = e0;
                                              Cookiefileinzip = Cookiefilepath;
                                              Cookiefilepath = Appfiledir;
                                              Appfiledir = Libnameinapk;
                                           }catch(Exception e0){	
                                              zipFile1 = zipFile;
                                              vi6 = vi9;
                                              vi5 = vi7;
                                              vi4 = vi8;
                                              firstloadfilepath = Cookiefileinzip;
                                              str2 = str1;
                                              vi2 = e0;
                                              Cookiefileinzip = Cookiefilepath;
                                              Cookiefilepath = Appfiledir;
                                              Appfiledir = Libnameinapk;
                                           }	
                                        label_1316 :
                                           if ((fileUnzip1 = zipFile1.getEntry(fileUnzip)) != null && !Util.isFileValid(new StringBuilder().append(Cookiefilepath).append(str).append(Util.libname).toString(), fileUnzip.getSize())) {	
                                              Util.UnzipFile(zipFile1, new StringBuilder().append(str2).append(Appfiledir).toString(), new File(new StringBuilder().append(Cookiefilepath).append(str).append(Util.libname).toString()));
                                           }	
                                           if ((fileUnzip1 = zipFile1.getEntry(new StringBuilder().append(str2).append(Appfiledir).toString())) != null && !Util.isFileValid(new StringBuilder().append(Cookiefilepath).append(str).append(Util.securename6).toString(), fileUnzip.getSize())) {	
                                              Util.UnzipFile(zipFile1, new StringBuilder().append(str2).append(Util.securename6).toString(), new File(new StringBuilder().append(Cookiefilepath).append(str).append(Util.securename6).toString()));
                                           }	
                                           if ((fileUnzip1 = zipFile1.getEntry(new StringBuilder().append(str2).append(Appfiledir).toString())) != null && !Util.isFileValid(new StringBuilder().append(Cookiefilepath).append(str).append(Util.securename7).toString(), fileUnzip.getSize())) {	
                                              Util.UnzipFile(zipFile1, new StringBuilder().append(str2).append(Util.securename7).toString(), new File(new StringBuilder().append(Cookiefilepath).append(str).append(Util.securename7).toString()));
                                           }	
                                           if ((fileUnzip2 = zipFile1.getEntry(firstloadfilepath)) != null && !(fileUnzip3 = fileUnzip.getSize())) {	
                                              Util.UnzipFile(zipFile1, firstloadfilepath, new File(Cookiefileinzip));
                                           }	
                                           if (vi5) {	
                                              try{	
                                                 vi5.release();
                                                 if (vi6) {	
                                                    try{	
                                                       vi6.close();
                                                       if (vi4) {	
                                                          try{	
                                                             vi4.close();
                                                          }catch(java.io.IOException e0){	
                                                             e0.printStackTrace();
                                                             return -1;
                                                          }	
                                                       }	
                                                    }catch(java.io.IOException e0){	
                                                       e0.printStackTrace();
                                                       if (vi4) {	
                                                          try{	
                                                             vi4.close();
                                                             vi7 = -1;
                                                          }catch(java.io.IOException e0){	
                                                             e0.printStackTrace();
                                                             return -1;
                                                          }	
                                                       }else {	
                                                          vi7 = -1;
                                                       }	
                                                       return vi7;
                                                    }catch(Exception e0){	
                                                       vi2 = e0;
                                                       if (vi4) {	
                                                          try{	
                                                             vi4.close();
                                                          }catch(java.io.IOException e0){	
                                                             e0.printStackTrace();
                                                             return -1;
                                                          }	
                                                       }	
                                                       throw vi2;
                                                    }	
                                                 }	
                                              }catch(java.io.IOException e0){	
                                                 vi2 = e0;
                                                 try{	
                                                    vi2.printStackTrace();
                                                    if (vi6) {	
                                                       try{	
                                                          vi6.close();
                                                          if (vi4) {	
                                                             vi4.close();
                                                             vi7 = -1;
                                                          }else {	
                                                             vi7 = -1;
                                                          }	
                                                       }catch(java.io.IOException e0){	
                                                          e0.printStackTrace();
                                                          if (vi4) {	
                                                             try{	
                                                                vi4.close();
                                                                vi8 = -1;
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                return -1;
                                                             }	
                                                          }else {	
                                                             vi8 = -1;
                                                          }	
                                                          return vi8;
                                                       }catch(Exception e0){	
                                                          vi9 = e0;
                                                          if (vi4) {	
                                                             try{	
                                                                vi4.close();
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                return -1;
                                                             }	
                                                          }	
                                                          throw vi9;
                                                       }catch(java.io.IOException e0){	
                                                          e0.printStackTrace();
                                                          return -1;
                                                       }	
                                                    }else {	
                                                       vi7 = -1;
                                                    }	
                                                    return vi7;
                                                 }catch(Exception e0){	
                                                    vi2 = e0;
                                                    if (vi6) {	
                                                       try{	
                                                          vi6.close();
                                                          if (vi4) {	
                                                             try{	
                                                                vi4.close();
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                return -1;
                                                             }	
                                                          }	
                                                       }catch(java.io.IOException e0){	
                                                          e0.printStackTrace();
                                                          if (vi4) {	
                                                             try{	
                                                                vi4.close();
                                                                vi7 = -1;
                                                             }catch(java.io.IOException e0){	
                                                                e0.printStackTrace();
                                                                return -1;
                                                             }	
                                                          }else {	
                                                             vi7 = -1;
                                                          }	
                                                          return vi7;
                                                       }catch(Exception e0){	
                                                          vi2 = e0;
                                                          if (vi4) {	
                                                             vi4.close();
                                                          }	
                                                          throw vi2;
                                                       }	
                                                    }	
                                                    throw vi2;
                                                 }	
                                              }catch(Exception e0){	
                                              }	
                                           }	
                                           throw vi2;
                                        }catch(java.lang.Exception e0){	
                                           zipFile1 = zipFile;
                                           firstloadfilepath = Cookiefileinzip;
                                           str2 = str1;
                                           vi2 = e0;
                                           Cookiefileinzip = Cookiefilepath;
                                           Cookiefilepath = Appfiledir;
                                           Appfiledir = Libnameinapk;
                                        }catch(Exception e0){	
                                           zipFile1 = zipFile;
                                           vi6 = vi9;
                                           vi5 = vi7;
                                           vi4 = vi8;
                                           firstloadfilepath = Cookiefileinzip;
                                           str2 = str1;
                                           vi2 = e0;
                                           Cookiefileinzip = Cookiefilepath;
                                           Cookiefilepath = Appfiledir;
                                           Appfiledir = Libnameinapk;
                                           goto label_1316 ;	
                                        }	
                                     }catch(java.lang.Exception e0){	
                                        zipFile1 = zipFile;
                                        firstloadfilepath = Cookiefileinzip;
                                        str2 = str1;
                                        vi2 = e0;
                                        Cookiefileinzip = Cookiefilepath;
                                        Cookiefilepath = Appfiledir;
                                        Appfiledir = Libnameinapk;
                                     }catch(Exception e0){	
                                        zipFile1 = zipFile;
                                        vi6 = vi9;
                                        vi5 = vi7;
                                        vi4 = vi8;
                                        firstloadfilepath = Cookiefileinzip;
                                        str2 = str1;
                                        vi2 = e0;
                                        Cookiefileinzip = Cookiefilepath;
                                        Cookiefilepath = Appfiledir;
                                        Appfiledir = Libnameinapk;
                                        goto label_1316 ;	
                                     }	
                                  }catch(java.lang.Exception e0){	
                                     zipFile1 = zipFile;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     vi2 = e0;
                                     Cookiefileinzip = Cookiefilepath;
                                     Cookiefilepath = Appfiledir;
                                     Appfiledir = Libnameinapk;
                                  }catch(Exception e0){	
                                     zipFile1 = zipFile;
                                     vi6 = vi9;
                                     vi5 = vi7;
                                     vi4 = vi8;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     vi2 = e0;
                                     Cookiefileinzip = Cookiefilepath;
                                     Cookiefilepath = Appfiledir;
                                     Appfiledir = Libnameinapk;
                                     goto label_1316 ;	
                                  }	
                               }else {	
                                  vi14 = backupfilepath;
                                  vi12 = firstloadfilepath;
                                  vi17 = Cookiefilepath;
                               }	
                               if (vi == -1 || vi == -3) {	
                                  try{	
                                     Process.killProcess(Process.myPid());
                                     System.exit(0);
                                  }catch(java.lang.Exception e0){	
                                     zipFile1 = zipFile;
                                     Cookiefilepath = Appfiledir;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     Appfiledir = Libnameinapk;
                                     vi2 = e0;
                                     Cookiefileinzip = vi17;
                                     vi17 = vi12;
                                  }catch(Exception e0){	
                                     zipFile1 = zipFile;
                                     vi6 = vi9;
                                     vi5 = vi7;
                                     vi4 = vi8;
                                     Cookiefilepath = Appfiledir;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     Appfiledir = Libnameinapk;
                                     vi2 = e0;
                                     Cookiefileinzip = vi17;
                                     vi17 = vi12;
                                     goto label_1316 ;	
                                  }	
                               }	
                            }catch(java.lang.Exception e0){	
                               zipFile1 = zipFile;
                               firstloadfilepath = Cookiefileinzip;
                               str2 = str1;
                               vi2 = e0;
                               Cookiefileinzip = Cookiefilepath;
                               Cookiefilepath = Appfiledir;
                               Appfiledir = Libnameinapk;
                            }catch(Exception e0){	
                               zipFile1 = zipFile;
                               vi6 = vi9;
                               vi5 = vi7;
                               vi4 = vi8;
                               firstloadfilepath = Cookiefileinzip;
                               str2 = str1;
                               vi2 = e0;
                               Cookiefileinzip = Cookiefilepath;
                               Cookiefilepath = Appfiledir;
                               Appfiledir = Libnameinapk;
                               goto label_1316 ;	
                            }	
                         }else {	
                            vi14 = backupfilepath;
                            vi12 = firstloadfilepath;
                            vi17 = Cookiefilepath;
                         }	
                         try{	
                            Util.DeleteFile(new StringBuilder().append(Appfiledir).append(str).append(Util.libname).toString());
                            Util.DeleteFile(new StringBuilder().append(Appfiledir).append(str).append(Util.securename6).toString());
                            Cookiefile = -1;
                            fileUnzip8 = -1;
                            vi15 = -1;
                            vi16 = -1;
                            vi10 = 0;
                            while (true) {	
                               vi18 = Cookiefile;
                               vi = Util.MAX_DEX_NUM;
                               vi19 = fileUnzip8;
                               sstr = "/odexdir/";
                               vi20 = Cookiefilepath;
                               Cookiefilepath = "/oat/arm64/";
                               vi21 = vi16;
                               firstloadfilepath = "/oat/arm/";
                               vi4 = vi8;
                               str3 = "_";
                               vi6 = vi9;
                               str4 = ".";
                               if (file_count < deletedexresult) {	
                                  try{	
                                     sappend2 = new StringBuilder().append(Appfiledir).append(str);
                                     try{	
                                        vi5 = vi7;
                                        securename0 = Util.securename0;
                                        vi22 = Libnameinapk;
                                        try{	
                                           vi = Util.DeleteFile(deletedexresult);
                                           sappend3 = new StringBuilder().append(Appfiledir).append(str);
                                           Libnameinapk = Util.securename1;
                                           try{	
                                              vi23 = Cookiefileinzip;
                                              Cookiefileinzip1 = new StringBuilder();
                                              vi7 = Util.DeleteFile(deletejarresult);
                                              Cookiefileinzip1 = new StringBuilder().append(Appfiledir).append(sstr);
                                              Libnameinapk = Util.securename0;
                                              try{	
                                                 vi24 = vi17;
                                                 vi17 = Util.DeleteFile(deleteodexresult);
                                                 vi25 = sstr;
                                                 fileUnzip8 = Util.DeleteFile(deleteflagresult);
                                                 vi18 = deleteflagresult;
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename14, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename15, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename14, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 Util.DeleteFile(new StringBuilder().append(Appfiledir).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename15, str4, new StringBuilder().append(str3).append(file_count).toString())).toString());
                                                 fileUnzip8 = -1;
                                                 if (fileUnzip8 == deletedexresult && fileUnzip8 == deletejarresult && fileUnzip8 == deleteodexresult) {	
                                                    vi20 = deleteodexresult;
                                                    try{	
                                                    label_0723 :
                                                       backupfilepath = Util.securename9;
                                                       Util.DeleteFile(new StringBuilder().append(Appfiledir).append(str).append(deleteodexresult).toString());
                                                       Util.DeleteFile(new StringBuilder().append(Appfiledir).append(str).append(Util.securename5).toString());
                                                       Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Util.securename5).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename5).toString()));
                                                       Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Util.libname).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.libname).toString()));
                                                       Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Util.securename6).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename6).toString()));
                                                       Util.UnzipFile(zipFile, new StringBuilder().append(str1).append(Util.securename7).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString()));
                                                       try{	
                                                          vi17 = vi24;
                                                          Appprofiledir = new File(vi17);
                                                          try{	
                                                             Cookiefileinzip = vi23;
                                                             Util.UnzipFile(zipFile, Cookiefileinzip, Appprofiledir);
                                                             fileUnzip8 = 0;
                                                             while (true) {	
                                                                if (file_count2 < file_count) {	
                                                                   try{	
                                                                      stringBuilde = new StringBuilder();
                                                                      try{	
                                                                         str2 = vi14;
                                                                         stringBuilde = stringBuilde.append(deletedexresult).append(str);
                                                                         securename0 = Util.securename0;
                                                                         try{	
                                                                            vi24 = vi17;
                                                                            backupfilepath = firstloadfilepath.toString();
                                                                            vi7 = Util.DeleteFile(deletedexresult);
                                                                            firstloadfilepath1 = new StringBuilder();
                                                                            firstloadfilepath1 = firstloadfilepath.append(deletedexresult).append(str);
                                                                            Libnameinapk = Util.securename1;
                                                                            try{	
                                                                               vi23 = Cookiefileinzip;
                                                                               backupfilepath = firstloadfilepath.toString();
                                                                               vi17 = Util.DeleteFile(firstloadfilepath);
                                                                               Libnameinapk = vi25;
                                                                               Cookiefileinzip1 = new StringBuilder().append(deletedexresult).append(Libnameinapk);
                                                                               try{	
                                                                                  vi21 = Appfiledir;
                                                                                  Appfiledir = Util.securename0;
                                                                                  vi2 = Util.DeleteFile(deleteodexresult);
                                                                                  sappend4 = new StringBuilder().append(deletedexresult).append(Libnameinapk);
                                                                                  Cookiefileinzip = Util.securename8;
                                                                                  try{	
                                                                                     vi25 = str1;
                                                                                     Util.DeleteFile(new StringBuilder().append(deletedexresult).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count2).toString())).toString());
                                                                                     Util.DeleteFile(new StringBuilder().append(deletedexresult).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count2).toString())).toString());
                                                                                     Util.DeleteFile(new StringBuilder().append(deletedexresult).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename14, str4, new StringBuilder().append(str3).append(file_count2).toString())).toString());
                                                                                     Util.DeleteFile(new StringBuilder().append(deletedexresult).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename15, str4, new StringBuilder().append(str3).append(file_count2).toString())).toString());
                                                                                     Util.DeleteFile(new StringBuilder().append(deletedexresult).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename14, str4, new StringBuilder().append(str3).append(file_count2).toString())).toString());
                                                                                     Appfiledir = Libnameinapk.toString();
                                                                                     Util.DeleteFile(Libnameinapk);
                                                                                     sappend4 = -1;
                                                                                     if (Libnameinapk == deletedexresult && Libnameinapk == deletejarresult && Libnameinapk == deleteodexresult) {	
                                                                                        vi14 = deletejarresult;
                                                                                     label_0b0d :
                                                                                        file_count3 = 0;
                                                                                        while (true) {	
                                                                                           if (file_count3 < file_count) {	
                                                                                              try{	
                                                                                                 stringBuilde1 = new StringBuilder();
                                                                                                 try{	
                                                                                                    backupfilepath = vi12;
                                                                                                    vi7 = Util.DeleteFile(stringBuilde1.append(deletejarresult).append(str).append(Util.CreatenewFileName(Util.securename0, str4, new StringBuilder().append(str3).append(file_count3).toString())).toString());
                                                                                                    fileUnzip8 = Util.DeleteFile(deletejarresult);
                                                                                                    deleteodexresult = new StringBuilder();
                                                                                                    vi2 = Util.DeleteFile(deleteodexresult);
                                                                                                    Cookiefileinzip1 = new StringBuilder().append(deletejarresult).append(Libnameinapk);
                                                                                                    str1 = Util.securename8;
                                                                                                    try{	
                                                                                                       vi19 = deletedexresult;
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count3).toString())).toString());
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(Cookiefilepath).append(Util.CreatenewFileName(Util.securename11, str4, new StringBuilder().append(str3).append(file_count3).toString())).toString());
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(firstloadfilepath).append(Util.CreatenewFileName(Util.securename14, str4, new StringBuilder().append(str3).append(file_count3).toString())).toString());
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(firstloadfilepath).append(Cookiefilepath).toString());
                                                                                                       Cookiefileinzip = Util.securename14;
                                                                                                       Cookiefileinzip = Util.CreatenewFileName(Cookiefilepath, str4, new StringBuilder().append(str3).append(file_count3).toString());
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(Cookiefilepath).append(Cookiefilepath).toString());
                                                                                                       Cookiefileinzip = Util.securename15;
                                                                                                       Cookiefileinzip = Util.CreatenewFileName(Cookiefilepath, str4, new StringBuilder().append(str3).append(file_count3).toString());
                                                                                                       Util.DeleteFile(new StringBuilder().append(deletejarresult).append(Cookiefilepath).append(Cookiefilepath).toString());
                                                                                                       sappend2 = -1;
                                                                                                       if (sappend2 == deletedexresult && sappend2 == deletejarresult && sappend2 == deleteodexresult) {	
                                                                                                       label_0d6a :
                                                                                                          deleteodexresult = new StringBuilder();
                                                                                                          str2 = vi25;
                                                                                                          Appfiledir = vi22;
                                                                                                          zipFile1 = apkzf;
                                                                                                          if ((fileUnzip4 = deletejarresult.getEntry(fileUnzip)) != null) {	
                                                                                                             Cookiefilepath = vi21;
                                                                                                             if (!Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Util.libname).toString(), fileUnzip.getSize())) {	
                                                                                                                firstloadfilepath = Util.libname;
                                                                                                                Util.UnzipFile(deletejarresult, new StringBuilder().append(str2).append(Libnameinapk).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Cookiefileinzip).toString()));
                                                                                                             }	
                                                                                                          }else {	
                                                                                                             Cookiefilepath = vi21;
                                                                                                          }	
                                                                                                          if ((fileUnzip4 = deletejarresult.getEntry(new StringBuilder().append(str2).append(Libnameinapk).toString())) != null && !Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Util.securename6).toString(), fileUnzip.getSize())) {	
                                                                                                             firstloadfilepath = Util.securename6;
                                                                                                             Util.UnzipFile(deletejarresult, new StringBuilder().append(str2).append(Util.securename6).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Cookiefileinzip).toString()));
                                                                                                          }	
                                                                                                          if ((fileUnzip4 = deletejarresult.getEntry(new StringBuilder().append(str2).append(Libnameinapk).toString())) != null && !Util.isFileValid(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString(), fileUnzip.getSize())) {	
                                                                                                             Util.UnzipFile(deletejarresult, new StringBuilder().append(str2).append(Util.securename7).toString(), new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString()));
                                                                                                          }	
                                                                                                          firstloadfilepath = vi23;
                                                                                                          if ((fileUnzip4 = deletejarresult.getEntry(Cookiefileinzip)) != null) {	
                                                                                                             Cookiefileinzip = vi24;
                                                                                                             if (!Util.isFileValid(Cookiefilepath, fileUnzip.getSize())) {	
                                                                                                                Util.UnzipFile(deletejarresult, Cookiefileinzip, new File(Cookiefilepath));
                                                                                                             }	
                                                                                                          }else {	
                                                                                                             Cookiefilepath3 = vi24;
                                                                                                          }	
                                                                                                          if (vi5) {	
                                                                                                             try{	
                                                                                                                vi5.release();
                                                                                                                if (vi6) {	
                                                                                                                   try{	
                                                                                                                      vi6.close();
                                                                                                                      vi4.close();
                                                                                                                      break ;	
                                                                                                                   }catch(java.io.IOException e0){	
                                                                                                                      e0.printStackTrace();
                                                                                                                      try{	
                                                                                                                         vi4.close();
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         return -1;
                                                                                                                      }	
                                                                                                                   }catch(Exception e0){	
                                                                                                                      vi = e0;
                                                                                                                      try{	
                                                                                                                         vi4.close();
                                                                                                                         throw vi;
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         return -1;
                                                                                                                      }	
                                                                                                                   }catch(java.io.IOException e0){	
                                                                                                                      e0.printStackTrace();
                                                                                                                      return -1;
                                                                                                                   }	
                                                                                                                }	
                                                                                                             }catch(java.io.IOException e0){	
                                                                                                                vi = e0;
                                                                                                                try{	
                                                                                                                   vi.printStackTrace();
                                                                                                                   if (vi6) {	
                                                                                                                      try{	
                                                                                                                         vi6.close();
                                                                                                                         vi4.close();
                                                                                                                         vi7 = -1;
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         try{	
                                                                                                                            vi4.close();
                                                                                                                            return -1;
                                                                                                                         }catch(java.io.IOException e0){	
                                                                                                                            e0.printStackTrace();
                                                                                                                            return -1;
                                                                                                                         }	
                                                                                                                      }catch(Exception e0){	
                                                                                                                         vi9 = e0;
                                                                                                                         try{	
                                                                                                                            vi4.close();
                                                                                                                            throw vi9;
                                                                                                                         }catch(java.io.IOException e0){	
                                                                                                                            e0.printStackTrace();
                                                                                                                            return -1;
                                                                                                                         }	
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         return -1;
                                                                                                                      }	
                                                                                                                   }else {	
                                                                                                                      vi7 = -1;
                                                                                                                   }	
                                                                                                                   return vi7;
                                                                                                                }catch(Exception e0){	
                                                                                                                   try{	
                                                                                                                      vi = e0;
                                                                                                                      if (vi6) {	
                                                                                                                         vi6.close();
                                                                                                                         try{	
                                                                                                                            vi4.close();
                                                                                                                         }catch(java.io.IOException e0){	
                                                                                                                            e0.printStackTrace();
                                                                                                                            return -1;
                                                                                                                         }	
                                                                                                                      }	
                                                                                                                      throw vi;
                                                                                                                   }catch(java.io.IOException e0){	
                                                                                                                      e0.printStackTrace();
                                                                                                                      try{	
                                                                                                                         vi4.close();
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         return -1;
                                                                                                                      }	
                                                                                                                   }catch(Exception e0){	
                                                                                                                      vi = e0;
                                                                                                                      try{	
                                                                                                                         vi4.close();
                                                                                                                         throw vi;
                                                                                                                      }catch(java.io.IOException e0){	
                                                                                                                         e0.printStackTrace();
                                                                                                                         return -1;
                                                                                                                      }	
                                                                                                                   }	
                                                                                                                   return -1;
                                                                                                                }	
                                                                                                             }catch(Exception e0){	
                                                                                                             }	
                                                                                                          }	
                                                                                                          break ;	
                                                                                                       }else {	
                                                                                                          sappend2 = -2;
                                                                                                          if (sappend2 != deletedexresult && sappend2 == deletejarresult || sappend2 == deleteodexresult) {	
                                                                                                             Cookiefilepath3 = Process.myPid();
                                                                                                             Process.killProcess(Cookiefilepath);
                                                                                                             System.exit(0);
                                                                                                          }	
                                                                                                          file_count3++;
                                                                                                          vi12 = deletejarresult;
                                                                                                          vi = vi19;
                                                                                                       }	
                                                                                                    }catch(java.lang.Exception e0){	
                                                                                                       zipFile1 = apkzf;
                                                                                                       vi2 = e0;
                                                                                                       Cookiefilepath = vi21;
                                                                                                       vi8 = vi4;
                                                                                                       vi9 = vi6;
                                                                                                       vi7 = vi5;
                                                                                                       Appfiledir = vi22;
                                                                                                       firstloadfilepath = vi23;
                                                                                                       Cookiefileinzip = vi24;
                                                                                                       str2 = vi25;
                                                                                                    }catch(Exception e0){	
                                                                                                       zipFile1 = apkzf;
                                                                                                       vi2 = e0;
                                                                                                       Cookiefilepath = vi21;
                                                                                                       Appfiledir = vi22;
                                                                                                       firstloadfilepath = vi23;
                                                                                                       Cookiefileinzip = vi24;
                                                                                                       str2 = vi25;
                                                                                                       goto label_1316 ;	
                                                                                                    }	
                                                                                                 }catch(java.lang.Exception e0){	
                                                                                                    zipFile1 = apkzf;
                                                                                                    vi2 = e0;
                                                                                                    Cookiefilepath = vi21;
                                                                                                    vi8 = vi4;
                                                                                                    vi9 = vi6;
                                                                                                    vi7 = vi5;
                                                                                                    Appfiledir = vi22;
                                                                                                    firstloadfilepath = vi23;
                                                                                                    Cookiefileinzip = vi24;
                                                                                                    str2 = vi25;
                                                                                                 }catch(Exception e0){	
                                                                                                    zipFile1 = apkzf;
                                                                                                    vi2 = e0;
                                                                                                    Cookiefilepath = vi21;
                                                                                                    Appfiledir = vi22;
                                                                                                    firstloadfilepath = vi23;
                                                                                                    Cookiefileinzip = vi24;
                                                                                                    str2 = vi25;
                                                                                                    goto label_1316 ;	
                                                                                                 }	
                                                                                              }catch(java.lang.Exception e0){	
                                                                                                 vi17 = vi12;
                                                                                                 zipFile1 = apkzf;
                                                                                                 vi2 = e0;
                                                                                                 Cookiefilepath = vi21;
                                                                                                 vi8 = vi4;
                                                                                                 vi9 = vi6;
                                                                                                 vi7 = vi5;
                                                                                                 Appfiledir = vi22;
                                                                                                 firstloadfilepath = vi23;
                                                                                                 Cookiefileinzip = vi24;
                                                                                                 str2 = vi25;
                                                                                              }catch(Exception e0){	
                                                                                                 vi17 = vi12;
                                                                                                 zipFile1 = apkzf;
                                                                                                 vi2 = e0;
                                                                                                 Cookiefilepath = vi21;
                                                                                                 Appfiledir = vi22;
                                                                                                 firstloadfilepath = vi23;
                                                                                                 Cookiefileinzip = vi24;
                                                                                                 str2 = vi25;
                                                                                                 goto label_1316 ;	
                                                                                              }	
                                                                                           }else {	
                                                                                              vi17 = vi12;
                                                                                              vi12 = deleteodexresult;
                                                                                              goto label_0d6a ;	
                                                                                           }	
                                                                                        }	
                                                                                        vi2 = 0;
                                                                                        return fileUnzip;
                                                                                     }else {	
                                                                                        file_count3 = -2;
                                                                                        if (file_count3 != deletedexresult && file_count3 == deletejarresult || file_count3 == deleteodexresult) {	
                                                                                           Process.killProcess(Process.myPid());
                                                                                           System.exit(0);
                                                                                        }	
                                                                                        file_count2++;
                                                                                        vi20 = deleteodexresult;
                                                                                        vi14 = deletedexresult;
                                                                                        vi = deletedexresult;
                                                                                        vi7 = deletejarresult;
                                                                                        file_count3 = vi21;
                                                                                        Cookiefilepath3 = vi23;
                                                                                        vi17 = vi24;
                                                                                        vi26 = vi25;
                                                                                        vi2 = apkzf;
                                                                                        vi25 = Libnameinapk;
                                                                                     }	
                                                                                  }catch(java.lang.Exception e0){	
                                                                                     zipFile1 = apkzf;
                                                                                     vi2 = e0;
                                                                                     vi17 = vi12;
                                                                                     Cookiefilepath = vi21;
                                                                                     vi8 = vi4;
                                                                                     vi9 = vi6;
                                                                                     vi7 = vi5;
                                                                                     Appfiledir = vi22;
                                                                                     firstloadfilepath = vi23;
                                                                                     Cookiefileinzip = vi24;
                                                                                     str2 = vi25;
                                                                                  }catch(Exception e0){	
                                                                                     zipFile1 = apkzf;
                                                                                     vi2 = e0;
                                                                                     vi17 = vi12;
                                                                                     Cookiefilepath = vi21;
                                                                                     Appfiledir = vi22;
                                                                                     firstloadfilepath = vi23;
                                                                                     Cookiefileinzip = vi24;
                                                                                     str2 = vi25;
                                                                                     goto label_1316 ;	
                                                                                  }	
                                                                               }catch(java.lang.Exception e0){	
                                                                                  zipFile1 = apkzf;
                                                                                  vi2 = e0;
                                                                                  str2 = str1;
                                                                                  vi17 = vi12;
                                                                                  Cookiefilepath = vi21;
                                                                                  vi8 = vi4;
                                                                                  vi9 = vi6;
                                                                                  vi7 = vi5;
                                                                                  Appfiledir = vi22;
                                                                                  firstloadfilepath = vi23;
                                                                                  Cookiefileinzip = vi24;
                                                                               }catch(Exception e0){	
                                                                                  zipFile1 = apkzf;
                                                                                  vi2 = e0;
                                                                                  str2 = str1;
                                                                                  vi17 = vi12;
                                                                                  Cookiefilepath = vi21;
                                                                                  Appfiledir = vi22;
                                                                                  firstloadfilepath = vi23;
                                                                                  Cookiefileinzip = vi24;
                                                                                  goto label_1316 ;	
                                                                               }	
                                                                            }catch(java.lang.Exception e0){	
                                                                               zipFile1 = apkzf;
                                                                               vi2 = e0;
                                                                               Cookiefilepath = Appfiledir;
                                                                               str2 = str1;
                                                                               vi8 = vi4;
                                                                               vi9 = vi6;
                                                                               vi7 = vi5;
                                                                               Appfiledir = vi22;
                                                                               firstloadfilepath = vi23;
                                                                               Cookiefileinzip = vi24;
                                                                            }catch(Exception e0){	
                                                                               zipFile1 = apkzf;
                                                                               vi2 = e0;
                                                                               Cookiefilepath = Appfiledir;
                                                                               str2 = str1;
                                                                               Appfiledir = vi22;
                                                                               firstloadfilepath = vi23;
                                                                               Cookiefileinzip = vi24;
                                                                               goto label_1316 ;	
                                                                            }	
                                                                         }catch(java.lang.Exception e0){	
                                                                            zipFile1 = apkzf;
                                                                            vi2 = e0;
                                                                            Cookiefilepath = Appfiledir;
                                                                            firstloadfilepath = Cookiefileinzip;
                                                                            str2 = str1;
                                                                            vi8 = vi4;
                                                                            vi9 = vi6;
                                                                            vi7 = vi5;
                                                                            Appfiledir = vi22;
                                                                            Cookiefileinzip = vi24;
                                                                         }catch(Exception e0){	
                                                                            zipFile1 = apkzf;
                                                                            vi2 = e0;
                                                                            Cookiefilepath = Appfiledir;
                                                                            firstloadfilepath = Cookiefileinzip;
                                                                            str2 = str1;
                                                                            Appfiledir = vi22;
                                                                            Cookiefileinzip = vi24;
                                                                            goto label_1316 ;	
                                                                         }	
                                                                      }catch(java.lang.Exception e0){	
                                                                         zipFile1 = apkzf;
                                                                         vi2 = e0;
                                                                         Cookiefilepath = Appfiledir;
                                                                         firstloadfilepath = Cookiefileinzip;
                                                                         str2 = str1;
                                                                         vi8 = vi4;
                                                                         vi9 = vi6;
                                                                         vi7 = vi5;
                                                                         Appfiledir = vi22;
                                                                         Cookiefileinzip = vi17;
                                                                         vi17 = vi12;
                                                                      }catch(Exception e0){	
                                                                         zipFile1 = apkzf;
                                                                         vi2 = e0;
                                                                         Cookiefilepath = Appfiledir;
                                                                         firstloadfilepath = Cookiefileinzip;
                                                                         str2 = str1;
                                                                         Appfiledir = vi22;
                                                                         Cookiefileinzip = vi17;
                                                                         vi17 = vi12;
                                                                         goto label_1316 ;	
                                                                      }	
                                                                   }catch(java.lang.Exception e0){	
                                                                      zipFile1 = apkzf;
                                                                      vi2 = e0;
                                                                      Cookiefilepath = Appfiledir;
                                                                      firstloadfilepath = Cookiefileinzip;
                                                                      str2 = str1;
                                                                      vi8 = vi4;
                                                                      vi9 = vi6;
                                                                      vi7 = vi5;
                                                                      Appfiledir = vi22;
                                                                      Cookiefileinzip = vi17;
                                                                      vi17 = vi12;
                                                                   }catch(Exception e0){	
                                                                      zipFile1 = apkzf;
                                                                      vi2 = e0;
                                                                      Cookiefilepath = Appfiledir;
                                                                      firstloadfilepath = Cookiefileinzip;
                                                                      str2 = str1;
                                                                      Appfiledir = vi22;
                                                                      Cookiefileinzip = vi17;
                                                                      vi17 = vi12;
                                                                      goto label_1316 ;	
                                                                   }	
                                                                }else {	
                                                                   vi21 = Appfiledir;
                                                                   vi24 = vi17;
                                                                   vi23 = Cookiefileinzip;
                                                                   vi = vi14;
                                                                   Libnameinapk = vi25;
                                                                   vi25 = str1;
                                                                   vi7 = deletedexresult;
                                                                   vi2 = vi20;
                                                                   goto label_0b0d ;	
                                                                }	
                                                             }	
                                                          }catch(java.lang.Exception e0){	
                                                             zipFile1 = zipFile;
                                                             Cookiefilepath = Appfiledir;
                                                             firstloadfilepath = new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString();
                                                             str2 = str1;
                                                             Appfiledir = vi22;
                                                             Cookiefileinzip = vi17;
                                                             vi2 = e0;
                                                             vi8 = vi4;
                                                             vi9 = vi6;
                                                             vi7 = vi5;
                                                          }catch(Exception e0){	
                                                             zipFile1 = zipFile;
                                                             Cookiefilepath = Appfiledir;
                                                             firstloadfilepath = new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString();
                                                             str2 = str1;
                                                             Appfiledir = vi22;
                                                             Cookiefileinzip = vi17;
                                                             vi2 = e0;
                                                             goto label_1316 ;	
                                                          }	
                                                       }catch(java.lang.Exception e0){	
                                                          zipFile1 = zipFile;
                                                          Cookiefilepath = Appfiledir;
                                                          Cookiefileinzip = new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString());
                                                          str2 = str1;
                                                          Appfiledir = vi22;
                                                          firstloadfilepath = vi23;
                                                          vi2 = e0;
                                                          vi8 = vi4;
                                                          vi9 = vi6;
                                                          vi7 = vi5;
                                                       }catch(Exception e0){	
                                                          zipFile1 = zipFile;
                                                          Cookiefilepath = Appfiledir;
                                                          Cookiefileinzip = new File(new StringBuilder().append(Appfiledir).append(str).append(Util.securename7).toString());
                                                          str2 = str1;
                                                          Appfiledir = vi22;
                                                          firstloadfilepath = vi23;
                                                          vi2 = e0;
                                                          goto label_1316 ;	
                                                       }	
                                                    }catch(java.lang.Exception e0){	
                                                       zipFile1 = zipFile;
                                                       Cookiefilepath = Appfiledir;
                                                       str2 = str1;
                                                       vi17 = vi12;
                                                       Appfiledir = vi22;
                                                       firstloadfilepath = vi23;
                                                       Cookiefileinzip = vi24;
                                                       vi2 = e0;
                                                       vi8 = vi4;
                                                       vi9 = vi6;
                                                       vi7 = vi5;
                                                    }catch(Exception e0){	
                                                       zipFile1 = zipFile;
                                                       Cookiefilepath = Appfiledir;
                                                       str2 = str1;
                                                       vi17 = vi12;
                                                       Appfiledir = vi22;
                                                       firstloadfilepath = vi23;
                                                       Cookiefileinzip = vi24;
                                                       vi2 = e0;
                                                       goto label_1316 ;	
                                                    }	
                                                 }else {	
                                                    str4 = -2;
                                                    if (str4 != deletedexresult && str4 == deletejarresult || str4 == deleteodexresult) {	
                                                       Process.killProcess(Process.myPid());
                                                       System.exit(0);
                                                    }	
                                                    file_count++;
                                                    fileUnzip8 = deletejarresult;
                                                    vi15 = deleteodexresult;
                                                    vi16 = vi18;
                                                    vi8 = vi4;
                                                    vi9 = vi6;
                                                    vi7 = vi5;
                                                    vi27 = vi22;
                                                    Cookiefilepath3 = vi23;
                                                    vi17 = vi24;
                                                 }	
                                              }catch(java.lang.Exception e0){	
                                                 zipFile1 = zipFile;
                                                 Cookiefilepath = Appfiledir;
                                                 str2 = str1;
                                                 vi8 = vi4;
                                                 vi9 = vi6;
                                                 vi7 = vi5;
                                                 Appfiledir = vi22;
                                                 firstloadfilepath = vi23;
                                                 Cookiefileinzip = v30;
                                                 vi2 = e0;
                                              }catch(Exception e0){	
                                                 zipFile1 = zipFile;
                                                 Cookiefilepath = Appfiledir;
                                                 str2 = str1;
                                                 Appfiledir = vi22;
                                                 firstloadfilepath = vi23;
                                                 Cookiefileinzip = v30;
                                                 vi2 = e0;
                                                 goto label_1316 ;	
                                              }	
                                           }catch(java.lang.Exception e0){	
                                              zipFile1 = zipFile;
                                              Cookiefilepath = Appfiledir;
                                              Cookiefileinzip = vi17;
                                              str2 = str1;
                                              vi8 = vi4;
                                              vi9 = vi6;
                                              vi7 = vi5;
                                              Appfiledir = vi22;
                                              firstloadfilepath = v29;
                                              vi2 = e0;
                                           }catch(Exception e0){	
                                              zipFile1 = zipFile;
                                              Cookiefilepath = Appfiledir;
                                              Cookiefileinzip = vi17;
                                              str2 = str1;
                                              Appfiledir = vi22;
                                              firstloadfilepath = v29;
                                              vi2 = e0;
                                              goto label_1316 ;	
                                           }	
                                        }catch(java.lang.Exception e0){	
                                           zipFile1 = zipFile;
                                           Cookiefilepath = Appfiledir;
                                           firstloadfilepath = Cookiefileinzip;
                                           str2 = str1;
                                           vi8 = vi4;
                                           vi9 = vi6;
                                           vi7 = vi5;
                                           Appfiledir = vi22;
                                           vi2 = e0;
                                           Cookiefileinzip = vi17;
                                           vi17 = vi12;
                                        }catch(Exception e0){	
                                           zipFile1 = zipFile;
                                           Cookiefilepath = Appfiledir;
                                           firstloadfilepath = Cookiefileinzip;
                                           str2 = str1;
                                           Appfiledir = vi22;
                                           vi2 = e0;
                                           Cookiefileinzip = vi17;
                                           vi17 = vi12;
                                           goto label_1316 ;	
                                        }	
                                     }catch(java.lang.Exception e0){	
                                        zipFile1 = zipFile;
                                        Cookiefilepath = Appfiledir;
                                        firstloadfilepath = Cookiefileinzip;
                                        str2 = str1;
                                        Appfiledir = Libnameinapk;
                                        vi8 = vi4;
                                        vi9 = vi6;
                                        vi7 = v27;
                                        vi2 = e0;
                                        Cookiefileinzip = vi17;
                                        vi17 = vi12;
                                     }catch(Exception e0){	
                                        zipFile1 = zipFile;
                                        Cookiefilepath = Appfiledir;
                                        firstloadfilepath = Cookiefileinzip;
                                        str2 = str1;
                                        Appfiledir = Libnameinapk;
                                        vi2 = e0;
                                        Cookiefileinzip = vi17;
                                        vi17 = vi12;
                                        goto label_1316 ;	
                                     }	
                                  }catch(java.lang.Exception e0){	
                                     zipFile1 = zipFile;
                                     Cookiefilepath = Appfiledir;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     Appfiledir = Libnameinapk;
                                     vi8 = vi4;
                                     vi9 = vi6;
                                     vi2 = e0;
                                     Cookiefileinzip = vi17;
                                     vi17 = vi12;
                                  }catch(Exception e0){	
                                     vi5 = vi7;
                                     zipFile1 = zipFile;
                                     Cookiefilepath = Appfiledir;
                                     firstloadfilepath = Cookiefileinzip;
                                     str2 = str1;
                                     Appfiledir = Libnameinapk;
                                     vi2 = e0;
                                     Cookiefileinzip = vi17;
                                     vi17 = vi12;
                                     goto label_1316 ;	
                                  }	
                               }else {	
                                  vi5 = vi7;
                                  vi25 = sstr;
                                  vi24 = vi17;
                                  vi23 = Cookiefileinzip;
                                  vi22 = Libnameinapk;
                                  vi = vi18;
                                  vi7 = vi19;
                                  vi18 = vi21;
                                  goto label_0723 ;	
                               }	
                            }	
                         }catch(java.lang.Exception e0){	
                            zipFile1 = zipFile;
                            Cookiefilepath = Appfiledir;
                            firstloadfilepath = Cookiefileinzip;
                            str2 = str1;
                            Appfiledir = Libnameinapk;
                            Cookiefileinzip = vi17;
                            vi2 = e0;
                         }catch(Exception e0){	
                            zipFile1 = zipFile;
                            vi6 = vi9;
                            vi5 = vi7;
                            vi4 = vi8;
                            Cookiefilepath = Appfiledir;
                            firstloadfilepath = Cookiefileinzip;
                            str2 = str1;
                            Appfiledir = Libnameinapk;
                            Cookiefileinzip = vi17;
                            vi2 = e0;
                            goto label_1316 ;	
                         }	
                      }catch(java.lang.Exception e0){	
                         firstloadfilepath = Cookiefileinzip;
                         str2 = str1;
                         zipFile1 = zipFile;
                         Cookiefileinzip = Cookiefilepath;
                         Cookiefilepath = Appfiledir;
                         Appfiledir = Libnameinapk;
                         vi2 = e0;
                      }catch(Exception e0){	
                         vi6 = vi9;
                         vi5 = vi7;
                         vi4 = vi8;
                         firstloadfilepath = Cookiefileinzip;
                         str2 = str1;
                         zipFile1 = zipFile;
                         Cookiefileinzip = Cookiefilepath;
                         Cookiefilepath = Appfiledir;
                         Appfiledir = Libnameinapk;
                         vi2 = e0;
                         goto label_1316 ;	
                      }	
                   }catch(java.lang.Exception e0){	
                      firstloadfilepath = Cookiefileinzip;
                      str2 = str1;
                      zipFile1 = zipFile;
                      Cookiefileinzip = Cookiefilepath;
                      Cookiefilepath = Appfiledir;
                      Appfiledir = Libnameinapk;
                      vi2 = e0;
                      vi3 = vi1;
                   }catch(Exception e0){	
                      vi6 = vi9;
                      vi5 = 0;
                      vi4 = vi8;
                      firstloadfilepath = Cookiefileinzip;
                      str2 = str1;
                      zipFile1 = zipFile;
                      Cookiefileinzip = Cookiefilepath;
                      Cookiefilepath = Appfiledir;
                      Appfiledir = Libnameinapk;
                      vi2 = e0;
                      vi3 = vi1;
                      goto label_1316 ;	
                   }	
                }catch(java.lang.Exception e0){	
                   firstloadfilepath = Cookiefileinzip;
                   str2 = str1;
                   zipFile1 = zipFile;
                   Cookiefileinzip = Cookiefilepath;
                   Cookiefilepath = Appfiledir;
                   Appfiledir = Libnameinapk;
                   vi2 = e0;
                   vi3 = vi1;
                }catch(Exception e0){	
                   vi6 = 0;
                   vi4 = vi8;
                   firstloadfilepath = Cookiefileinzip;
                   str2 = str1;
                   zipFile1 = zipFile;
                   Cookiefileinzip = Cookiefilepath;
                   Cookiefilepath = Appfiledir;
                   Appfiledir = Libnameinapk;
                   vi2 = e0;
                   vi5 = 0;
                   vi3 = vi1;
                   goto label_1316 ;	
                }	
             }catch(java.lang.Exception e0){	
                firstloadfilepath = Cookiefileinzip;
                str2 = str1;
                zipFile1 = zipFile;
                Cookiefileinzip = Cookiefilepath;
                Cookiefilepath = Appfiledir;
                Appfiledir = Libnameinapk;
                vi2 = e0;
                vi3 = vi1;
             }catch(Exception e0){	
                vi4 = 0;
                firstloadfilepath = Cookiefileinzip;
                str2 = str1;
                zipFile1 = zipFile;
                Cookiefileinzip = Cookiefilepath;
                Cookiefilepath = Appfiledir;
                Appfiledir = Libnameinapk;
                vi2 = e0;
                vi6 = 0;
                vi5 = 0;
                vi3 = vi1;
                goto label_1316 ;	
             }	
          }catch(java.lang.Exception e0){	
             firstloadfilepath = Cookiefileinzip;
             str2 = str1;
             zipFile1 = zipFile;
             Cookiefileinzip = Cookiefilepath;
             Cookiefilepath = Appfiledir;
             Appfiledir = Libnameinapk;
             vi2 = e0;
             vi3 = vi1;
          }catch(Exception e0){	
             firstloadfilepath = Cookiefileinzip;
             str2 = str1;
             zipFile1 = zipFile;
             Cookiefileinzip = Cookiefilepath;
             Cookiefilepath = Appfiledir;
             Appfiledir = Libnameinapk;
             vi2 = e0;
             vi6 = 0;
             vi5 = 0;
             vi4 = 0;
             vi3 = vi1;
             goto label_1316 ;	
          }	
       }catch(java.lang.Exception e0){	
          firstloadfilepath = Cookiefileinzip;
          str2 = str1;
          zipFile1 = zipFile;
          Cookiefileinzip = Cookiefilepath;
          Cookiefilepath = Appfiledir;
          Appfiledir = Libnameinapk;
          vi2 = e0;
          vi3 = vi;
       }catch(Exception e0){	
          firstloadfilepath = Cookiefileinzip;
          str2 = str1;
          zipFile1 = zipFile;
          Cookiefileinzip = Cookiefilepath;
          Cookiefilepath = Appfiledir;
          Appfiledir = Libnameinapk;
          vi2 = e0;
          vi6 = 0;
          vi5 = 0;
          vi4 = 0;
          vi3 = vi;
          goto label_1316 ;	
       }	
       if (!Util.isFileValid(new StringBuilder().append(Cookiefilepath).append(str).append(Util.libname).toString(), fileUnzip.getSize())) {	
       }else {	
       }	
    }
    public static boolean SafeUnzipFile(ZipFile zf,String filepathinzip,File fileinfiledir){	
       return Util.SafeUnzipFile(zf, filepathinzip, fileinfiledir, 0);
    }
    public static boolean SafeUnzipFile(ZipFile zf,String filepathinzip,File fileinfiledir,long crc){	
       ZipEntry ze;
       int vi = 0;
       boolean vb = false;
       try{	
          if (!(ze = zf.getEntry(filepathinzip))) {	
             if (vi) {	
                try{	
                   vi.close();
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e6){	
                         e6.printStackTrace();
                         return vb;
                      }	
                   }	
                }catch(java.io.IOException e6){	
                   e6.printStackTrace();
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e7){	
                         e7.printStackTrace();
                         return vb;
                      }	
                   }	
                   return vb;
                }catch(Exception e6){	
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e6){	
                         e6.printStackTrace();
                         return vb;
                      }	
                   }	
                   throw e6;
                }	
             }	
             return vb;
          }else if(crc && !ze.getCrc()-crc){		
             if (vi) {	
                try{	
                   vi.close();
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e6){	
                         e6.printStackTrace();
                         return vb;
                      }	
                   }	
                }catch(java.io.IOException e6){	
                   e6.printStackTrace();
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e7){	
                         e7.printStackTrace();
                         return vb;
                      }	
                   }	
                   return vb;
                }catch(Exception e6){	
                   if (0) {	
                      try{	
                         0.close();
                      }catch(java.io.IOException e6){	
                         e6.printStackTrace();
                         return vb;
                      }	
                   }	
                   throw e6;
                }	
             }	
             return true;
          }else {	
             int vi1 = 1;
             try{	
                byte[] buf = Util.UnzipFile(zf, ze);
                if (vi1) {	
                   vi = new BufferedOutputStream(new FileOutputStream(fileinfiledir));
                   vi.write(buf, vb, buf.length);
                }	
                if (vi) {	
                   try{	
                      vi.close();
                      if (0) {	
                         try{	
                            0.close();
                         }catch(java.io.IOException e6){	
                            e6.printStackTrace();
                            return vb;
                         }	
                      }	
                   }catch(java.io.IOException e6){	
                      e6.printStackTrace();
                      if (0) {	
                         try{	
                            0.close();
                         }catch(java.io.IOException e7){	
                            e7.printStackTrace();
                            return vb;
                         }	
                      }	
                      return vb;
                   }catch(Exception e6){	
                      if (0) {	
                         try{	
                            0.close();
                         }catch(java.io.IOException e6){	
                            e6.printStackTrace();
                            return vb;
                         }	
                      }	
                      throw e6;
                   }	
                }	
                return true;
             }catch(java.lang.Exception e6){	
                try{	
                   e6.printStackTrace();
                   if (vi) {	
                      try{	
                         vi.close();
                         if (0) {	
                            try{	
                               0.close();
                            }catch(java.io.IOException e7){	
                               e7.printStackTrace();
                               return vb;
                            }	
                         }	
                      }catch(java.io.IOException e7){	
                         e7.printStackTrace();
                         if (0) {	
                            try{	
                               0.close();
                            }catch(java.io.IOException e8){	
                               e8.printStackTrace();
                               return vb;
                            }	
                         }	
                         return vb;
                      }catch(Exception e7){	
                         if (0) {	
                            try{	
                               0.close();
                            }catch(java.io.IOException e7){	
                               e7.printStackTrace();
                               return vb;
                            }	
                         }	
                         throw e7;
                      }	
                   }	
                   return vb;
                }catch(Exception e6){	
                   if (vi) {	
                      try{	
                         vi.close();
                         if (0) {	
                            try{	
                               0.close();
                            }catch(java.io.IOException e6){	
                               e6.printStackTrace();
                               return vb;
                            }	
                         }	
                      }catch(java.io.IOException e6){	
                         e6.printStackTrace();
                         if (0) {	
                            try{	
                               0.close();
                            }catch(java.io.IOException e7){	
                               e7.printStackTrace();
                               return vb;
                            }	
                         }	
                         return vb;
                      }catch(Exception e6){	
                         if (0) {	
                            0.close();
                         }	
                         throw e6;
                      }	
                   }	
                   throw e6;
                }	
             }catch(Exception e6){	
             }	
          }	
       }catch(java.lang.Exception e6){	
       }catch(Exception e6){	
       }	
    }
    public static boolean UnzipFile(ZipFile zf,String filepathinzip,File fileinfiledir){	
       ZipEntry ze;
       int readlen;
       int vi = 0;
       int vi1 = 0;
       boolean vb = false;
       try{	
          if (!(ze = zf.getEntry(filepathinzip))) {	
             if (vi) {	
                try{	
                   vi.close();
                   if (vi1) {	
                      try{	
                         vi1.close();
                      }catch(java.io.IOException e4){	
                         e4.printStackTrace();
                         return vb;
                      }	
                   }	
                }catch(java.io.IOException e4){	
                   e4.printStackTrace();
                   if (vi1) {	
                      try{	
                         vi1.close();
                      }catch(java.io.IOException e5){	
                         e5.printStackTrace();
                         return vb;
                      }	
                   }	
                   return vb;
                }catch(Exception e4){	
                   if (vi1) {	
                      try{	
                         vi1.close();
                      }catch(java.io.IOException e4){	
                         e4.printStackTrace();
                         return vb;
                      }	
                   }	
                   throw e4;
                }	
             }	
             return vb;
          }else {	
             try{	
                vi = new BufferedOutputStream(new FileOutputStream(fileinfiledir));
                byte[] buf = new byte[buf];
                vi1 = new BufferedInputStream(zf.getInputStream(ze));
                while ((readlen = vi1.read(buf)) >= 0) {	
                   try{	
                      vi.write(buf, vb, readlen);
                   }catch(java.lang.Exception e4){	
                      e4.printStackTrace();
                      if (vi) {	
                         try{	
                            vi.close();
                            if (vi1) {	
                               try{	
                                  vi1.close();
                               }catch(java.io.IOException e5){	
                                  e5.printStackTrace();
                                  return vb;
                               }	
                            }	
                         }catch(java.io.IOException e5){	
                            e5.printStackTrace();
                            if (vi1) {	
                               try{	
                                  vi1.close();
                               }catch(java.io.IOException e6){	
                                  e6.printStackTrace();
                                  return vb;
                               }	
                            }	
                            return vb;
                         }catch(Exception e5){	
                            if (vi1) {	
                               try{	
                                  vi1.close();
                               }catch(java.io.IOException e5){	
                                  e5.printStackTrace();
                                  return vb;
                               }	
                            }	
                            throw e5;
                         }	
                      }	
                      return vb;
                   }catch(Exception e4){	
                      if (vi) {	
                         try{	
                            vi.close();
                            if (vi1) {	
                               try{	
                                  vi1.close();
                               }catch(java.io.IOException e4){	
                                  e4.printStackTrace();
                                  return vb;
                               }	
                            }	
                         }catch(java.io.IOException e4){	
                            e4.printStackTrace();
                            if (vi1) {	
                               try{	
                                  vi1.close();
                               }catch(java.io.IOException e5){	
                                  e5.printStackTrace();
                                  return vb;
                               }	
                            }	
                            return vb;
                         }catch(Exception e4){	
                            if (vi1) {	
                               vi1.close();
                            }	
                            throw e4;
                         }	
                      }	
                      throw e4;
                   }	
                }	
                try{	
                   vi.close();
                   vi1.close();
                   return true;
                }catch(java.io.IOException e4){	
                   e4.printStackTrace();
                   try{	
                      vi1.close();
                      return vb;
                   }catch(java.io.IOException e5){	
                      readlen.printStackTrace();
                      return vb;
                   }	
                }catch(Exception e4){	
                   try{	
                      vi1.close();
                      throw e4;
                   }catch(java.io.IOException e4){	
                      e4.printStackTrace();
                      return vb;
                   }	
                }catch(java.io.IOException e4){	
                   e4.printStackTrace();
                   return vb;
                }	
             }catch(java.lang.Exception e4){	
             }catch(Exception e4){	
             }	
          }	
       }catch(java.lang.Exception e4){	
       }catch(Exception e4){	
       }	
    }
    public static byte[] UnzipFile(ZipFile zf,ZipEntry ze){	
       int readlen;
       byte[] buf = new byte[(int)ze.getSize()];
       BufferedInputStream bufbr = new BufferedInputStream(zf.getInputStream(ze));
       int totallen = 0;
       while ((readlen = bufbr.read(buf, totallen, ((int)ze.getSize()-totallen))) >= 0) {	
          totallen = totallen+readlen;
          if (!(long)totallen-ze.getSize()) {	
             break ;	
          }	
       }	
       long readlen1 = ze.getSize();
       if (totallen == (int)readlen) {	
          return buf;
       }	
       throw new IOException("incorrect zip file size");
    }
    private static boolean checkCopiedFileCrc(ZipFile zf,String filepathinzip,File file){	
       long crc = Util.getFileCRC32(file);
       boolean vb = false;
       if (!crc--1) {	
          return vb;
       }	
       int vi = 0;
       try{	
          if (!(vi = zf.getEntry(filepathinzip))) {	
             return vb;
          }	
          if (crc && !vi.getCrc()-crc) {	
             return true;
          }	
          return vb;
       }catch(java.lang.Exception e4){	
          return vb;
       }	
    }
    public static boolean deleteDir(File file){	
       int i;
       boolean success;
       boolean result = true;
       if (file.isDirectory()) {	
          String[] children = file.list();
          i = 0;
          while (true) {	
             if (i < children.length) {	
                if (!(success = Util.deleteDir(success))) {	
                   return false;
                }else {	
                   i++;
                }	
             }	
          }	
       }	
       if (file.exists()) {	
          result = file.delete();
       }	
       return result;
    }
    public static long getCRC32(File fileUri){	
       int vi;
       CRC32 crc32 = new CRC32();
       try{	
          vi = 0;
          int vi1 = new CheckedInputStream(new BufferedInputStream(new FileInputStream(fileUri)), crc32);
          byte[] buf = new byte[buf];
          do {	
          } while (vi1.read(buf) >= 0);	
          long crc = vi1.getChecksum().getValue();
          vi1.close();
       }catch(java.io.FileNotFoundException e5){	
          e5.printStackTrace();
       }catch(java.io.IOException e5){	
          e5.printStackTrace();
       }catch(Exception e5){	
          throw e5;
       }	
       return v3;
    }
    private static long getFileCRC32(File file){	
       CRC32 crc32;
       int totallen;
       int readlen;
       long result = -1;
       byte[] filebuf = new byte[(int)file.length()];
       try{	
          crc32 = new CRC32();
          int vi = new BufferedInputStream(new FileInputStream(file));
          for (totallen = 0; (readlen = vi.read(filebuf)) >= 0; totallen = totallen+readlen) {	
             crc32.update(filebuf);
          }	
          long readlen1 = crc32.getValue();
          result = readlen;
          vi.close();
       }catch(java.io.FileNotFoundException e5){	
          e5.printStackTrace();
          if (0) {	
             0.close();
          }	
       }catch(java.io.IOException e5){	
          e5.printStackTrace();
          if (0) {	
             0.close();
          }	
       }catch(Exception e5){	
          if (0) {	
             0.close();
          }	
          throw e5;
       }catch(java.io.IOException e5){	
          e5.printStackTrace();
       }catch( e0){	
       }	
       return result;
    }
    public static String getelffilearch(String elffile){	
       String file_arch;
       int archcode = Util.readelfarch(elffile);
       if (archcode != 3) {	
          if (archcode != 40) {	
             if (archcode != 62) {	
                file_arch = (archcode != 183)? "unknown": "arm64-v8a";	
             }else {	
                file_arch = "86_64";
             }	
          }else {	
             file_arch = "armeabi";
          }	
       }else {	
          file_arch = "86";
       }	
       return file_arch;
    }
    private static boolean isFileValid(String path,long length){	
       File tmpfile = new File(path);
       if (tmpfile.exists() && !tmpfile.length()-length) {	
          return true;
       }	
       return false;
    }
    public static int readelfarch(String filename){	
       int vi = 0;
       try{	
          vi = new RandomAccessFile(filename, "r");
          vi.seek(18);
          int c = vi.read();
          try{	
             vi.close();
          }catch(java.io.IOException e2){	
             e2.printStackTrace();
          }	
       label_0034 :
          return 0;
       }catch(java.io.FileNotFoundException e2){	
          e2.printStackTrace();
          if (vi) {	
             try{	
                vi.close();
                goto label_0034 ;	
             }catch(java.io.IOException e2){	
             }	
          }else {	
             goto label_0034 ;	
          }	
       }catch(java.io.IOException e2){	
          e2.printStackTrace();
          if (vi) {	
             try{	
                vi.close();
                goto label_0034 ;	
             }catch(java.io.IOException e2){	
             }	
          }else {	
             goto label_0034 ;	
          }	
       }catch(Exception e2){	
          if (vi) {	
             vi.close();
          }	
          throw e2;
       }	
    }
}
