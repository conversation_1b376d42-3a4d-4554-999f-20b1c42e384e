//go:build windows

package main

import (
	"fmt"
	"strings"
	"unsafe"

	"golang.org/x/sys/windows"
)

var (
	modsetupapi                           = windows.NewLazySystemDLL("setupapi.dll")
	procSetupDiGetClassDevsW              = modsetupapi.NewProc("SetupDiGetClassDevsW")
	procSetupDiEnumDeviceInfo             = modsetupapi.NewProc("SetupDiEnumDeviceInfo")
	procSetupDiGetDeviceRegistryPropertyW = modsetupapi.NewProc("SetupDiGetDeviceRegistryPropertyW")
	procSetupDiDestroyDeviceInfoList      = modsetupapi.NewProc("SetupDiDestroyDeviceInfoList")
)

// GUID_DEVCLASS_USB = {36fc9e60-c465-11cf-8056-************}
var GUID_DEVCLASS_USB = windows.GUID{
	Data1: 0x36fc9e60,
	Data2: 0xc465,
	Data3: 0x11cf,
	Data4: [8]byte{0x80, 0x56, 0x44, 0x45, 0x53, 0x54, 0x00, 0x00},
}

const (
	DIGCF_PRESENT         = 0x00000002
	SPDRP_DEVICEDESC      = 0x00000000
	SPDRP_HARDWAREID      = 0x00000001
	SPDRP_SERVICE         = 0x00000004
	SPDRP_DRIVER          = 0x00000009
	SPDRP_ENUMERATOR_NAME = 0x00000016
	SPDRP_INF_PATH        = 0x0000000A
)

type SP_DEVINFO_DATA struct {
	cbSize    uint32
	ClassGuid windows.GUID
	DevInst   uint32
	Reserved  uintptr
}

func getRegistryProperty(deviceInfoSet uintptr, devInfoData *SP_DEVINFO_DATA, property uint32) string {
	var dataType uint32
	var buffer [512]uint16
	var requiredSize uint32

	ret, _, _ := procSetupDiGetDeviceRegistryPropertyW.Call(
		deviceInfoSet,
		uintptr(unsafe.Pointer(devInfoData)),
		uintptr(property),
		uintptr(unsafe.Pointer(&dataType)),
		uintptr(unsafe.Pointer(&buffer[0])),
		uintptr(len(buffer)*2),
		uintptr(unsafe.Pointer(&requiredSize)),
	)
	if ret == 0 {
		return ""
	}
	return windows.UTF16ToString(buffer[:])
}

func detectVirtual(enumerator, hwid, service, driverInf string) (bool, []string) {
	score := 0
	reasons := []string{}

	// 1. 枚举器
	if strings.EqualFold(enumerator, "ROOT") {
		score++
		reasons = append(reasons, "Enumerator=ROOT")
	}

	// 2. HardwareID
	if strings.Contains(strings.ToUpper(hwid), "ROOT\\") ||
		strings.Contains(strings.ToUpper(hwid), "VID_0000&PID_0000") {
		score++
		reasons = append(reasons, "Invalid HardwareID")
	}

	// 3. Service 驱动
	if strings.Contains(strings.ToLower(service), "vuhub3") ||
		strings.Contains(strings.ToLower(service), "vhci") ||
		strings.Contains(strings.ToLower(service), "usbip") {
		score++
		reasons = append(reasons, "Known virtual USB driver service")
	}

	// 4. INF 文件
	if strings.HasPrefix(strings.ToLower(driverInf), "oem") {
		score++
		reasons = append(reasons, "OEM INF file")
	}

	// 判定为虚拟的阈值
	isVirtual := score >= 2
	return isVirtual, reasons
}

func main() {
	deviceInfoSet, _, _ := procSetupDiGetClassDevsW.Call(
		uintptr(unsafe.Pointer(&GUID_DEVCLASS_USB)),
		0,
		0,
		DIGCF_PRESENT,
	)
	if deviceInfoSet == 0 {
		fmt.Println("❌ 无法获取 USB 设备信息")
		return
	}
	defer procSetupDiDestroyDeviceInfoList.Call(deviceInfoSet)

	fmt.Println("🔍 开始扫描 USB 控制器/Hub ...\n")

	var index uint32
	for {
		var devInfoData SP_DEVINFO_DATA
		devInfoData.cbSize = uint32(unsafe.Sizeof(devInfoData))

		ret, _, _ := procSetupDiEnumDeviceInfo.Call(
			deviceInfoSet,
			uintptr(index),
			uintptr(unsafe.Pointer(&devInfoData)),
		)
		if ret == 0 {
			break // 没有更多设备
		}

		desc := getRegistryProperty(deviceInfoSet, &devInfoData, SPDRP_DEVICEDESC)
		hwid := getRegistryProperty(deviceInfoSet, &devInfoData, SPDRP_HARDWAREID)
		service := getRegistryProperty(deviceInfoSet, &devInfoData, SPDRP_SERVICE)
		driverInf := getRegistryProperty(deviceInfoSet, &devInfoData, SPDRP_INF_PATH)
		enumerator := getRegistryProperty(deviceInfoSet, &devInfoData, SPDRP_ENUMERATOR_NAME)

		isVirtual, reasons := detectVirtual(enumerator, hwid, service, driverInf)

		fmt.Printf("设备: %s\n", desc)
		fmt.Printf("  Enumerator : %s\n", enumerator)
		fmt.Printf("  HardwareID : %s\n", hwid)
		fmt.Printf("  Service    : %s\n", service)
		fmt.Printf("  INF File   : %s\n", driverInf)

		if isVirtual {
			fmt.Printf("  ⚠️  检测为虚拟设备: %s\n", strings.Join(reasons, "; "))
		} else {
			fmt.Printf("  ✅ 检测为物理设备\n")
		}
		fmt.Println()
		index++
	}
}
