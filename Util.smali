###### Class com.wrapper.proxyapplication.Util (com.wrapper.proxyapplication.Util)
.class public Lcom/wrapper/proxyapplication/Util;
.super Ljava/lang/Object;
.source "Util.java"


# static fields
.field public static CPUABI:Ljava/lang/String; = null

.field static final ERROR_EXCEPTION:I = -0x2

.field static final ERROR_FALSE:I = 0x0

.field static final ERROR_FILE_EXIST:I = 0x2

.field static final ERROR_FILE_NOT_FOUND:I = -0x1

.field static final ERROR_FILE_NOT_FOUND_INZIP:I = -0x3

.field static final ERROR_SUCCESS:I = 0x1

.field public static MAX_DEX_NUM:I

.field public static TAG:Ljava/lang/String;

.field public static dexname:Ljava/lang/String;

.field public static ifoverwrite:Z

.field public static libname:Ljava/lang/String;

.field public static securename0:Ljava/lang/String;

.field public static securename1:Ljava/lang/String;

.field public static securename11:Ljava/lang/String;

.field public static securename14:Ljava/lang/String;

.field public static securename15:Ljava/lang/String;

.field public static securename2:Ljava/lang/String;

.field public static securename3:Ljava/lang/String;

.field public static securename4:Ljava/lang/String;

.field public static securename5:Ljava/lang/String;

.field public static securename6:Ljava/lang/String;

.field public static securename7:Ljava/lang/String;

.field public static securename8:Ljava/lang/String;

.field public static securename9:Ljava/lang/String;

.field public static simplelibname:Ljava/lang/String;

.field public static versionname:Ljava/lang/String;


# direct methods
.method static constructor <clinit>()V
    .registers 5

    .line 43
    const/16 v0, 0x12c

    sput v0, Lcom/wrapper/proxyapplication/Util;->MAX_DEX_NUM:I

    .line 44
    const-string v0, "Util"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->TAG:Ljava/lang/String;

    .line 45
    const/4 v0, 0x0

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->CPUABI:Ljava/lang/String;

    .line 46
    const-string v0, ""

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    .line 47
    sput-object v0, Lcom/wrapper/proxyapplication/Util;->simplelibname:Ljava/lang/String;

    .line 48
    const-string v0, "00O000ll111l.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;

    .line 49
    const-string v0, "00O000ll111l.jar"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename1:Ljava/lang/String;

    .line 50
    const-string v0, "000O00ll111l.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename2:Ljava/lang/String;

    .line 51
    const-string v0, "0000000lllll.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename3:Ljava/lang/String;

    .line 52
    const-string v0, "000000olllll.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename4:Ljava/lang/String;

    .line 55
    const-string v0, "0OO00l111l1l"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    .line 56
    const-string v0, "o0oooOO0ooOo.dat"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    .line 60
    const-string v0, "exportService.txt"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    .line 61
    const-string v0, ".flag00O000ll111l.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename8:Ljava/lang/String;

    .line 62
    const-string v0, ".updateIV.dat"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename9:Ljava/lang/String;

    .line 63
    const-string v0, "tosversion"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->versionname:Ljava/lang/String;

    .line 65
    const-string v0, ".flag00O000ll111l.vdex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    .line 68
    const-string v0, "00O000ll111l.vdex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    .line 69
    const-string v0, "00O000ll111l.odex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    .line 71
    const-string v0, "classes.dex"

    sput-object v0, Lcom/wrapper/proxyapplication/Util;->dexname:Ljava/lang/String;

    .line 73
    const/4 v0, 0x1

    sput-boolean v0, Lcom/wrapper/proxyapplication/Util;->ifoverwrite:Z

    .line 77
    invoke-static {}, Landroid/os/Process;->myTid()I

    move-result v0

    .line 78
    .local v0, "tid":I
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const-string v2, "/proc/"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v0}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    const-string v2, "/exe"

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-static {v1}, Lcom/wrapper/proxyapplication/Util;->getelffilearch(Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    sput-object v1, Lcom/wrapper/proxyapplication/Util;->CPUABI:Ljava/lang/String;

    .line 79
    sget-object v1, Lcom/wrapper/proxyapplication/Util;->CPUABI:Ljava/lang/String;

    const-string v2, ".so"

    const-string v3, "lib"

    const-string v4, "86"

    if-eq v1, v4, :cond_99

    const-string v4, "86_64"

    if-eq v1, v4, :cond_99

    .line 80
    const-string v1, "shell-super.com.btw.shenmou"

    sput-object v1, Lcom/wrapper/proxyapplication/Util;->simplelibname:Ljava/lang/String;

    .line 81
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v3, Lcom/wrapper/proxyapplication/Util;->simplelibname:Ljava/lang/String;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    sput-object v1, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    goto :goto_b3

    .line 84
    :cond_99
    const-string v1, "shellx-super.com.btw.shenmou"

    sput-object v1, Lcom/wrapper/proxyapplication/Util;->simplelibname:Ljava/lang/String;

    .line 85
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v3, Lcom/wrapper/proxyapplication/Util;->simplelibname:Ljava/lang/String;

    invoke-virtual {v1, v3}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    sput-object v1, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    .line 87
    .end local v0    # "tid":I
    :goto_b3
    return-void
.end method

.method public constructor <init>()V
    .registers 1

    .line 36
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    return-void
.end method

.method public static Comparetxtinzip(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)I
    .registers 15
    .param p0, "apkzf"    # Ljava/util/zip/ZipFile;
    .param p1, "filepathinzip"    # Ljava/lang/String;
    .param p2, "fileinfiledir"    # Ljava/io/File;

    .line 254
    const/4 v0, -0x1

    .line 256
    .local v0, "result":I
    const/4 v1, 0x0

    .line 258
    .local v1, "cookie_entry":Ljava/util/zip/ZipEntry;
    const/4 v2, 0x0

    .line 260
    .local v2, "checkzbr":Ljava/io/BufferedInputStream;
    const/4 v3, 0x0

    .line 262
    .local v3, "checkfbr":Ljava/io/BufferedInputStream;
    invoke-virtual {p0, p1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 265
    const/4 v4, -0x2

    if-nez v1, :cond_3c

    .line 267
    const/4 v5, -0x3

    .line 293
    if-eqz v2, :cond_3b

    .line 295
    :try_start_e
    invoke-virtual {v2}, Ljava/io/BufferedInputStream;->close()V
    :try_end_11
    .catch Ljava/io/IOException; {:try_start_e .. :try_end_11} :catch_1e
    .catchall {:try_start_e .. :try_end_11} :catchall_1c

    .line 301
    if-eqz v3, :cond_3b

    .line 303
    :try_start_13
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_16
    .catch Ljava/io/IOException; {:try_start_13 .. :try_end_16} :catch_17

    .line 308
    goto :goto_3b

    .line 304
    :catch_17
    move-exception v5

    .line 306
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 301
    .end local v5    # "e":Ljava/io/IOException;
    :catchall_1c
    move-exception v5

    goto :goto_2f

    .line 296
    :catch_1e
    move-exception v5

    .line 298
    .restart local v5    # "e":Ljava/io/IOException;
    :try_start_1f
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V
    :try_end_22
    .catchall {:try_start_1f .. :try_end_22} :catchall_1c

    .line 299
    nop

    .line 301
    if-eqz v3, :cond_2e

    .line 303
    :try_start_25
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_28
    .catch Ljava/io/IOException; {:try_start_25 .. :try_end_28} :catch_29

    .line 308
    goto :goto_2e

    .line 304
    :catch_29
    move-exception v6

    .line 306
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 299
    .end local v6    # "e":Ljava/io/IOException;
    :cond_2e
    :goto_2e
    return v4

    .line 301
    .end local v5    # "e":Ljava/io/IOException;
    :goto_2f
    if-eqz v3, :cond_3a

    .line 303
    :try_start_31
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_34
    .catch Ljava/io/IOException; {:try_start_31 .. :try_end_34} :catch_35

    .line 308
    goto :goto_3a

    .line 304
    :catch_35
    move-exception v5

    .line 306
    .restart local v5    # "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .end local v5    # "e":Ljava/io/IOException;
    :cond_3a
    :goto_3a
    throw v5

    .line 267
    :cond_3b
    :goto_3b
    return v5

    .line 270
    :cond_3c
    const/16 v5, 0x400

    :try_start_3e
    new-array v6, v5, [B

    .line 271
    .local v6, "checkzipbuf":[B
    new-array v5, v5, [B

    .line 272
    .local v5, "checkfilebuf":[B
    new-instance v7, Ljava/io/BufferedInputStream;

    invoke-virtual {p0, v1}, Ljava/util/zip/ZipFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v8

    invoke-direct {v7, v8}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    move-object v2, v7

    .line 273
    invoke-virtual {v2, v6}, Ljava/io/BufferedInputStream;->read([B)I

    move-result v7

    .line 274
    .local v7, "readziplen":I
    new-instance v8, Ljava/lang/String;

    invoke-direct {v8, v6}, Ljava/lang/String;-><init>([B)V

    const/4 v9, 0x0

    invoke-virtual {v8, v9, v7}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v8

    .line 277
    .local v8, "tmpzipstr":Ljava/lang/String;
    new-instance v10, Ljava/io/BufferedInputStream;

    new-instance v11, Ljava/io/FileInputStream;

    invoke-direct {v11, p2}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V

    invoke-direct {v10, v11}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    move-object v3, v10

    .line 278
    invoke-virtual {v3, v5}, Ljava/io/BufferedInputStream;->read([B)I

    move-result v10

    .line 279
    .local v10, "readfilelen":I
    new-instance v11, Ljava/lang/String;

    invoke-direct {v11, v5}, Ljava/lang/String;-><init>([B)V

    invoke-virtual {v11, v9, v10}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v9

    .line 282
    .local v9, "tmpfilestr":Ljava/lang/String;
    invoke-virtual {v9, v8}, Ljava/lang/String;->equals(Ljava/lang/Object;)Z

    move-result v11
    :try_end_76
    .catch Ljava/lang/Exception; {:try_start_3e .. :try_end_76} :catch_a9
    .catchall {:try_start_3e .. :try_end_76} :catchall_a7

    if-eqz v11, :cond_7a

    .line 283
    const/4 v0, 0x1

    goto :goto_7b

    .line 285
    :cond_7a
    const/4 v0, 0x0

    .line 293
    .end local v5    # "checkfilebuf":[B
    .end local v6    # "checkzipbuf":[B
    .end local v7    # "readziplen":I
    .end local v8    # "tmpzipstr":Ljava/lang/String;
    .end local v9    # "tmpfilestr":Ljava/lang/String;
    .end local v10    # "readfilelen":I
    :goto_7b
    nop

    .line 295
    :try_start_7c
    invoke-virtual {v2}, Ljava/io/BufferedInputStream;->close()V
    :try_end_7f
    .catch Ljava/io/IOException; {:try_start_7c .. :try_end_7f} :catch_8d
    .catchall {:try_start_7c .. :try_end_7f} :catchall_8b

    .line 301
    nop

    .line 303
    :try_start_80
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_83
    .catch Ljava/io/IOException; {:try_start_80 .. :try_end_83} :catch_86

    .line 308
    nop

    .line 307
    nop

    .line 313
    return v0

    .line 304
    :catch_86
    move-exception v5

    .line 306
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 301
    .end local v5    # "e":Ljava/io/IOException;
    :catchall_8b
    move-exception v5

    goto :goto_9d

    .line 296
    :catch_8d
    move-exception v5

    .line 298
    .restart local v5    # "e":Ljava/io/IOException;
    :try_start_8e
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V
    :try_end_91
    .catchall {:try_start_8e .. :try_end_91} :catchall_8b

    .line 299
    nop

    .line 301
    nop

    .line 303
    :try_start_93
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_96
    .catch Ljava/io/IOException; {:try_start_93 .. :try_end_96} :catch_98

    .line 308
    nop

    .line 299
    return v4

    .line 304
    :catch_98
    move-exception v6

    .line 306
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 303
    .end local v5    # "e":Ljava/io/IOException;
    .end local v6    # "e":Ljava/io/IOException;
    :goto_9d
    :try_start_9d
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_a0
    .catch Ljava/io/IOException; {:try_start_9d .. :try_end_a0} :catch_a2

    .line 308
    nop

    .line 307
    throw v5

    .line 304
    :catch_a2
    move-exception v5

    .line 306
    .restart local v5    # "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 293
    .end local v5    # "e":Ljava/io/IOException;
    :catchall_a7
    move-exception v5

    goto :goto_de

    .line 288
    :catch_a9
    move-exception v5

    .line 290
    .local v5, "e":Ljava/lang/Exception;
    :try_start_aa
    invoke-virtual {v5}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_ad
    .catchall {:try_start_aa .. :try_end_ad} :catchall_a7

    .line 291
    nop

    .line 293
    if-eqz v2, :cond_dd

    .line 295
    :try_start_b0
    invoke-virtual {v2}, Ljava/io/BufferedInputStream;->close()V
    :try_end_b3
    .catch Ljava/io/IOException; {:try_start_b0 .. :try_end_b3} :catch_c0
    .catchall {:try_start_b0 .. :try_end_b3} :catchall_be

    .line 301
    if-eqz v3, :cond_dd

    .line 303
    :try_start_b5
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_b8
    .catch Ljava/io/IOException; {:try_start_b5 .. :try_end_b8} :catch_b9

    .line 308
    goto :goto_dd

    .line 304
    :catch_b9
    move-exception v6

    .line 306
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 301
    .end local v6    # "e":Ljava/io/IOException;
    :catchall_be
    move-exception v6

    goto :goto_d1

    .line 296
    :catch_c0
    move-exception v6

    .line 298
    .restart local v6    # "e":Ljava/io/IOException;
    :try_start_c1
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V
    :try_end_c4
    .catchall {:try_start_c1 .. :try_end_c4} :catchall_be

    .line 299
    nop

    .line 301
    if-eqz v3, :cond_d0

    .line 303
    :try_start_c7
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_ca
    .catch Ljava/io/IOException; {:try_start_c7 .. :try_end_ca} :catch_cb

    .line 308
    goto :goto_d0

    .line 304
    :catch_cb
    move-exception v7

    .line 306
    .local v7, "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 299
    .end local v7    # "e":Ljava/io/IOException;
    :cond_d0
    :goto_d0
    return v4

    .line 301
    .end local v6    # "e":Ljava/io/IOException;
    :goto_d1
    if-eqz v3, :cond_dc

    .line 303
    :try_start_d3
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_d6
    .catch Ljava/io/IOException; {:try_start_d3 .. :try_end_d6} :catch_d7

    .line 308
    goto :goto_dc

    .line 304
    :catch_d7
    move-exception v6

    .line 306
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .end local v6    # "e":Ljava/io/IOException;
    :cond_dc
    :goto_dc
    throw v6

    .line 291
    :cond_dd
    :goto_dd
    return v4

    .line 293
    .end local v5    # "e":Ljava/lang/Exception;
    :goto_de
    if-eqz v2, :cond_10d

    .line 295
    :try_start_e0
    invoke-virtual {v2}, Ljava/io/BufferedInputStream;->close()V
    :try_end_e3
    .catch Ljava/io/IOException; {:try_start_e0 .. :try_end_e3} :catch_f0
    .catchall {:try_start_e0 .. :try_end_e3} :catchall_ee

    .line 301
    if-eqz v3, :cond_10d

    .line 303
    :try_start_e5
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_e8
    .catch Ljava/io/IOException; {:try_start_e5 .. :try_end_e8} :catch_e9

    .line 308
    goto :goto_10d

    .line 304
    :catch_e9
    move-exception v5

    .line 306
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 301
    .end local v5    # "e":Ljava/io/IOException;
    :catchall_ee
    move-exception v5

    goto :goto_101

    .line 296
    :catch_f0
    move-exception v5

    .line 298
    .restart local v5    # "e":Ljava/io/IOException;
    :try_start_f1
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V
    :try_end_f4
    .catchall {:try_start_f1 .. :try_end_f4} :catchall_ee

    .line 299
    nop

    .line 301
    if-eqz v3, :cond_100

    .line 303
    :try_start_f7
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_fa
    .catch Ljava/io/IOException; {:try_start_f7 .. :try_end_fa} :catch_fb

    .line 308
    goto :goto_100

    .line 304
    :catch_fb
    move-exception v6

    .line 306
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .line 299
    .end local v6    # "e":Ljava/io/IOException;
    :cond_100
    :goto_100
    return v4

    .line 301
    .end local v5    # "e":Ljava/io/IOException;
    :goto_101
    if-eqz v3, :cond_10c

    .line 303
    :try_start_103
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_106
    .catch Ljava/io/IOException; {:try_start_103 .. :try_end_106} :catch_107

    .line 308
    goto :goto_10c

    .line 304
    :catch_107
    move-exception v5

    .line 306
    .restart local v5    # "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 307
    return v4

    .end local v5    # "e":Ljava/io/IOException;
    :cond_10c
    :goto_10c
    throw v5

    :cond_10d
    :goto_10d
    throw v5
.end method

.method public static CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
    .registers 6
    .param p0, "Oldfilename"    # Ljava/lang/String;
    .param p1, "SplitString"    # Ljava/lang/String;
    .param p2, "InsertString"    # Ljava/lang/String;

    .line 691
    invoke-virtual {p0, p1}, Ljava/lang/String;->lastIndexOf(Ljava/lang/String;)I

    move-result v0

    .line 692
    .local v0, "index":I
    if-ltz v0, :cond_26

    .line 693
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    const/4 v2, 0x0

    invoke-virtual {p0, v2, v0}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, p2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {p0}, Ljava/lang/String;->length()I

    move-result v2

    invoke-virtual {p0, v0, v2}, Ljava/lang/String;->substring(II)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    return-object v1

    .line 695
    :cond_26
    const/4 v1, 0x0

    return-object v1
.end method

.method public static DeleteFile(Ljava/lang/String;)I
    .registers 3
    .param p0, "filepath"    # Ljava/lang/String;

    .line 176
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 177
    .local v0, "tmpfile":Ljava/io/File;
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_15

    .line 178
    invoke-virtual {v0}, Ljava/io/File;->delete()Z

    move-result v1

    if-nez v1, :cond_13

    .line 179
    const/4 v1, -0x2

    return v1

    .line 181
    :cond_13
    const/4 v1, 0x1

    return v1

    .line 183
    :cond_15
    const/4 v1, -0x1

    return v1
.end method

.method public static PrepareSecurefiles(Landroid/content/Context;Ljava/util/zip/ZipFile;)I
    .registers 34
    .param p0, "ctx"    # Landroid/content/Context;
    .param p1, "apkzf"    # Ljava/util/zip/ZipFile;

    .line 338
    move-object/from16 v1, p1

    const/4 v2, 0x0

    .line 340
    .local v2, "Cookiefile":Ljava/io/File;
    const/4 v3, 0x0

    .line 341
    .local v3, "file_channel":Ljava/nio/channels/FileChannel;
    const/4 v4, 0x0

    .line 343
    .local v4, "file_lock":Ljava/nio/channels/FileLock;
    const/4 v5, 0x0

    .line 345
    .local v5, "raf":Ljava/io/RandomAccessFile;
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual/range {p0 .. p0}, Landroid/content/Context;->getFilesDir()Ljava/io/File;

    move-result-object v7

    invoke-virtual {v7}, Ljava/io/File;->getAbsolutePath()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v7, "/prodexdir"

    invoke-virtual {v6, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 348
    .local v6, "Appfiledir":Ljava/lang/String;
    new-instance v7, Ljava/io/File;

    invoke-direct {v7, v6}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 349
    .local v7, "Appprofiledir":Ljava/io/File;
    invoke-virtual {v7}, Ljava/io/File;->isDirectory()Z

    move-result v8

    if-nez v8, :cond_2d

    .line 350
    invoke-virtual {v7}, Ljava/io/File;->mkdir()Z

    .line 354
    :cond_2d
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v9, "/"

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->versionname:Ljava/lang/String;

    invoke-virtual {v8, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    .line 355
    .local v8, "Cookiefilepath":Ljava/lang/String;
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v11, "/backUp"

    invoke-virtual {v10, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    .line 356
    .local v10, "backupfilepath":Ljava/lang/String;
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    const-string v12, "/firstLoad"

    invoke-virtual {v11, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    .line 357
    .local v11, "firstloadfilepath":Ljava/lang/String;
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    const-string v13, "assets/"

    invoke-virtual {v12, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->versionname:Ljava/lang/String;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    .line 358
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    const-string v15, "t"

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v15, Lcom/wrapper/proxyapplication/Util;->CPUABI:Ljava/lang/String;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    .line 362
    .local v14, "Libnameinapk":Ljava/lang/String;
    :try_start_8b
    new-instance v15, Ljava/io/RandomAccessFile;
    :try_end_8d
    .catch Ljava/lang/Exception; {:try_start_8b .. :try_end_8d} :catch_10ef
    .catchall {:try_start_8b .. :try_end_8d} :catchall_10d6

    move-object/from16 v16, v2

    .end local v2    # "Cookiefile":Ljava/io/File;
    .local v16, "Cookiefile":Ljava/io/File;
    :try_start_8f
    const-string v2, "rw"

    invoke-direct {v15, v8, v2}, Ljava/io/RandomAccessFile;-><init>(Ljava/lang/String;Ljava/lang/String;)V
    :try_end_94
    .catch Ljava/lang/Exception; {:try_start_8f .. :try_end_94} :catch_10c6
    .catchall {:try_start_8f .. :try_end_94} :catchall_10af

    move-object v5, v15

    .line 364
    :try_start_95
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->getChannel()Ljava/nio/channels/FileChannel;

    move-result-object v2
    :try_end_99
    .catch Ljava/lang/Exception; {:try_start_95 .. :try_end_99} :catch_109c
    .catchall {:try_start_95 .. :try_end_99} :catchall_1085

    move-object v3, v2

    .line 366
    :try_start_9a
    invoke-virtual {v3}, Ljava/nio/channels/FileChannel;->lock()Ljava/nio/channels/FileLock;

    move-result-object v2
    :try_end_9e
    .catch Ljava/lang/Exception; {:try_start_9a .. :try_end_9e} :catch_1070
    .catchall {:try_start_9a .. :try_end_9e} :catchall_1059

    move-object v4, v2

    .line 373
    :try_start_9f
    new-instance v2, Ljava/io/File;

    invoke-direct {v2, v8}, Ljava/io/File;-><init>(Ljava/lang/String;)V
    :try_end_a4
    .catch Ljava/lang/Exception; {:try_start_9f .. :try_end_a4} :catch_1042
    .catchall {:try_start_9f .. :try_end_a4} :catchall_102b

    .line 375
    .end local v16    # "Cookiefile":Ljava/io/File;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    :try_start_a4
    invoke-virtual {v2}, Ljava/io/File;->length()J

    move-result-wide v16
    :try_end_a8
    .catch Ljava/lang/Exception; {:try_start_a4 .. :try_end_a8} :catch_1014
    .catchall {:try_start_a4 .. :try_end_a8} :catchall_ffd

    const-wide/16 v18, 0x0

    cmp-long v20, v16, v18

    if-eqz v20, :cond_41f

    .line 379
    :try_start_ae
    invoke-static {v1, v12, v2}, Lcom/wrapper/proxyapplication/Util;->Comparetxtinzip(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)I

    move-result v16
    :try_end_b2
    .catch Ljava/lang/Exception; {:try_start_ae .. :try_end_b2} :catch_40c
    .catchall {:try_start_ae .. :try_end_b2} :catchall_3f3

    move/from16 v17, v16

    .line 381
    .local v17, "compareResult":I
    const/4 v15, 0x1

    move-object/from16 v18, v2

    move/from16 v2, v17

    .end local v17    # "compareResult":I
    .local v2, "compareResult":I
    .local v18, "Cookiefile":Ljava/io/File;
    if-ne v2, v15, :cond_3b7

    :try_start_bb
    new-instance v15, Ljava/lang/StringBuilder;

    invoke-direct {v15}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v15, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_c3
    .catch Ljava/lang/Exception; {:try_start_bb .. :try_end_c3} :catch_3a6
    .catchall {:try_start_bb .. :try_end_c3} :catchall_38f

    move-object/from16 v17, v7

    .end local v7    # "Appprofiledir":Ljava/io/File;
    .local v17, "Appprofiledir":Ljava/io/File;
    :try_start_c5
    sget-object v7, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    invoke-virtual {v15, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v15}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v15, Ljava/io/File;
    :try_end_d0
    .catch Ljava/lang/Exception; {:try_start_c5 .. :try_end_d0} :catch_380
    .catchall {:try_start_c5 .. :try_end_d0} :catchall_36b

    move-object/from16 v19, v11

    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v19, "firstloadfilepath":Ljava/lang/String;
    :try_start_d2
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_dd
    .catch Ljava/lang/Exception; {:try_start_d2 .. :try_end_dd} :catch_35b
    .catchall {:try_start_d2 .. :try_end_dd} :catchall_345

    move-object/from16 v20, v10

    .end local v10    # "backupfilepath":Ljava/lang/String;
    .local v20, "backupfilepath":Ljava/lang/String;
    :try_start_df
    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-direct {v15, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 382
    invoke-static {v1, v7, v15}, Lcom/wrapper/proxyapplication/Util;->checkCopiedFileCrc(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    move-result v7

    if-eqz v7, :cond_320

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v10, Ljava/io/File;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v15, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v11, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    invoke-direct {v10, v11}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 383
    invoke-static {v1, v7, v10}, Lcom/wrapper/proxyapplication/Util;->checkCopiedFileCrc(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    move-result v7
    :try_end_11f
    .catch Ljava/lang/Exception; {:try_start_df .. :try_end_11f} :catch_337
    .catchall {:try_start_df .. :try_end_11f} :catchall_323

    if-eqz v7, :cond_31d

    .line 386
    nop

    .line 533
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v1, v10}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v10

    .line 534
    .local v10, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v10, :cond_187

    .line 535
    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v15, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v11, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v11

    move-object v15, v8

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v15, "Cookiefilepath":Ljava/lang/String;
    invoke-virtual {v10}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v7

    invoke-static {v11, v7, v8}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v7

    if-nez v7, :cond_184

    .line 536
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v8, Ljava/io/File;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v21, v10

    .end local v10    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .local v21, "fileUnzip":Ljava/util/zip/ZipEntry;
    sget-object v10, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v11, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    invoke-direct {v8, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v7, v8}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_18a

    .line 535
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v10    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_184
    move-object/from16 v21, v10

    .end local v10    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    goto :goto_18a

    .line 534
    .end local v15    # "Cookiefilepath":Ljava/lang/String;
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_187
    move-object v15, v8

    move-object/from16 v21, v10

    .line 539
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v10    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v15    # "Cookiefilepath":Ljava/lang/String;
    .restart local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :goto_18a
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v1, v7}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v7

    .line 540
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .local v7, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v7, :cond_1f0

    .line 541
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v8, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v10

    invoke-static {v8, v10, v11}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v8

    if-nez v8, :cond_1ed

    .line 542
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v8, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    new-instance v10, Ljava/io/File;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v21, v7

    .end local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    sget-object v7, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v11, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-direct {v10, v7}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v8, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_1f2

    .line 541
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_1ed
    move-object/from16 v21, v7

    .end local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    goto :goto_1f2

    .line 540
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_1f0
    move-object/from16 v21, v7

    .line 545
    .end local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :goto_1f2
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v1, v7}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v7

    .line 546
    .end local v21    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .restart local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v7, :cond_252

    .line 547
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v8, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    invoke-virtual {v7}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v10

    invoke-static {v8, v10, v11}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v8

    if-nez v8, :cond_252

    .line 548
    new-instance v8, Ljava/lang/StringBuilder;

    invoke-direct {v8}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v8, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v8, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v8}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v8

    new-instance v10, Ljava/io/File;

    new-instance v11, Ljava/lang/StringBuilder;

    invoke-direct {v11}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v11, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v9, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v11, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v11}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v9

    invoke-direct {v10, v9}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v8, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 551
    :cond_252
    invoke-virtual {v1, v12}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v7

    .line 552
    if-eqz v7, :cond_26c

    .line 553
    invoke-virtual {v7}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v8

    move-object v10, v15

    .end local v15    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    invoke-static {v10, v8, v9}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v8

    if-nez v8, :cond_26d

    .line 554
    new-instance v8, Ljava/io/File;

    invoke-direct {v8, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v12, v8}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_26d

    .line 552
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v15    # "Cookiefilepath":Ljava/lang/String;
    :cond_26c
    move-object v10, v15

    .line 557
    .end local v15    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    :cond_26d
    :goto_26d
    if-eqz v4, :cond_31b

    .line 559
    :try_start_26f
    invoke-virtual {v4}, Ljava/nio/channels/FileLock;->release()V
    :try_end_272
    .catch Ljava/io/IOException; {:try_start_26f .. :try_end_272} :catch_2ab
    .catchall {:try_start_26f .. :try_end_272} :catchall_2a8

    .line 565
    if-eqz v3, :cond_31b

    .line 567
    :try_start_274
    invoke-virtual {v3}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_277
    .catch Ljava/io/IOException; {:try_start_274 .. :try_end_277} :catch_288
    .catchall {:try_start_274 .. :try_end_277} :catchall_285

    .line 573
    nop

    .line 575
    :try_start_278
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_27b
    .catch Ljava/io/IOException; {:try_start_278 .. :try_end_27b} :catch_27e

    .line 580
    nop

    .line 579
    goto/16 :goto_31b

    .line 576
    :catch_27e
    move-exception v0

    move-object v8, v0

    .line 578
    .local v8, "e":Ljava/io/IOException;
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v9, -0x1

    return v9

    .line 573
    .end local v8    # "e":Ljava/io/IOException;
    :catchall_285
    move-exception v0

    move-object v8, v0

    goto :goto_29c

    .line 568
    :catch_288
    move-exception v0

    move-object v8, v0

    .line 570
    .restart local v8    # "e":Ljava/io/IOException;
    :try_start_28a
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V
    :try_end_28d
    .catchall {:try_start_28a .. :try_end_28d} :catchall_285

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_28f
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_292
    .catch Ljava/io/IOException; {:try_start_28f .. :try_end_292} :catch_295

    .line 580
    :goto_292
    nop

    .line 571
    const/4 v9, -0x1

    return v9

    .line 576
    :catch_295
    move-exception v0

    const/4 v9, -0x1

    move-object v11, v0

    .line 578
    .local v11, "e":Ljava/io/IOException;
    invoke-virtual {v11}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v9

    .line 575
    .end local v8    # "e":Ljava/io/IOException;
    .end local v11    # "e":Ljava/io/IOException;
    :goto_29c
    :try_start_29c
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_29f
    .catch Ljava/io/IOException; {:try_start_29c .. :try_end_29f} :catch_2a1

    .line 580
    nop

    .line 579
    throw v8

    .line 576
    :catch_2a1
    move-exception v0

    move-object v8, v0

    .line 578
    .restart local v8    # "e":Ljava/io/IOException;
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v9, -0x1

    return v9

    .line 565
    .end local v8    # "e":Ljava/io/IOException;
    :catchall_2a8
    move-exception v0

    move-object v8, v0

    goto :goto_2e8

    .line 560
    :catch_2ab
    move-exception v0

    move-object v8, v0

    .line 562
    .restart local v8    # "e":Ljava/io/IOException;
    :try_start_2ad
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V
    :try_end_2b0
    .catchall {:try_start_2ad .. :try_end_2b0} :catchall_2a8

    .line 563
    nop

    .line 565
    if-eqz v3, :cond_2e6

    .line 567
    :try_start_2b3
    invoke-virtual {v3}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_2b6
    .catch Ljava/io/IOException; {:try_start_2b3 .. :try_end_2b6} :catch_2c6
    .catchall {:try_start_2b3 .. :try_end_2b6} :catchall_2c3

    .line 573
    nop

    .line 575
    :try_start_2b7
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_2ba
    .catch Ljava/io/IOException; {:try_start_2b7 .. :try_end_2ba} :catch_2bc

    .line 580
    const/4 v11, -0x1

    goto :goto_2e7

    .line 576
    :catch_2bc
    move-exception v0

    move-object v9, v0

    .line 578
    .local v9, "e":Ljava/io/IOException;
    invoke-virtual {v9}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v11, -0x1

    return v11

    .line 573
    .end local v9    # "e":Ljava/io/IOException;
    :catchall_2c3
    move-exception v0

    move-object v9, v0

    goto :goto_2da

    .line 568
    :catch_2c6
    move-exception v0

    move-object v9, v0

    .line 570
    .restart local v9    # "e":Ljava/io/IOException;
    :try_start_2c8
    invoke-virtual {v9}, Ljava/io/IOException;->printStackTrace()V
    :try_end_2cb
    .catchall {:try_start_2c8 .. :try_end_2cb} :catchall_2c3

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_2cd
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_2d0
    .catch Ljava/io/IOException; {:try_start_2cd .. :try_end_2d0} :catch_2d3

    .line 580
    nop

    .line 571
    const/4 v11, -0x1

    return v11

    .line 576
    :catch_2d3
    move-exception v0

    const/4 v11, -0x1

    move-object v13, v0

    .line 578
    .local v13, "e":Ljava/io/IOException;
    invoke-virtual {v13}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v11

    .line 575
    .end local v9    # "e":Ljava/io/IOException;
    .end local v13    # "e":Ljava/io/IOException;
    :goto_2da
    :try_start_2da
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_2dd
    .catch Ljava/io/IOException; {:try_start_2da .. :try_end_2dd} :catch_2df

    .line 580
    nop

    .line 579
    throw v9

    .line 576
    :catch_2df
    move-exception v0

    move-object v9, v0

    .line 578
    .restart local v9    # "e":Ljava/io/IOException;
    invoke-virtual {v9}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v11, -0x1

    return v11

    .line 565
    .end local v9    # "e":Ljava/io/IOException;
    :cond_2e6
    const/4 v11, -0x1

    .line 563
    :goto_2e7
    return v11

    .line 565
    .end local v8    # "e":Ljava/io/IOException;
    :goto_2e8
    if-eqz v3, :cond_31a

    .line 567
    :try_start_2ea
    invoke-virtual {v3}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_2ed
    .catch Ljava/io/IOException; {:try_start_2ea .. :try_end_2ed} :catch_2fc
    .catchall {:try_start_2ea .. :try_end_2ed} :catchall_2f9

    .line 573
    nop

    .line 575
    :try_start_2ee
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_2f1
    .catch Ljava/io/IOException; {:try_start_2ee .. :try_end_2f1} :catch_2f2

    .line 580
    goto :goto_31a

    .line 576
    :catch_2f2
    move-exception v0

    move-object v8, v0

    .line 578
    .restart local v8    # "e":Ljava/io/IOException;
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v9, -0x1

    return v9

    .line 573
    .end local v8    # "e":Ljava/io/IOException;
    :catchall_2f9
    move-exception v0

    move-object v8, v0

    goto :goto_30e

    .line 568
    :catch_2fc
    move-exception v0

    move-object v8, v0

    .line 570
    .restart local v8    # "e":Ljava/io/IOException;
    :try_start_2fe
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V
    :try_end_301
    .catchall {:try_start_2fe .. :try_end_301} :catchall_2f9

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_303
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_306
    .catch Ljava/io/IOException; {:try_start_303 .. :try_end_306} :catch_307

    goto :goto_292

    .line 576
    :catch_307
    move-exception v0

    const/4 v9, -0x1

    move-object v11, v0

    .line 578
    .restart local v11    # "e":Ljava/io/IOException;
    invoke-virtual {v11}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v9

    .line 575
    .end local v8    # "e":Ljava/io/IOException;
    .end local v11    # "e":Ljava/io/IOException;
    :goto_30e
    :try_start_30e
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_311
    .catch Ljava/io/IOException; {:try_start_30e .. :try_end_311} :catch_313

    .line 580
    nop

    .line 579
    throw v8

    .line 576
    :catch_313
    move-exception v0

    move-object v8, v0

    .line 578
    .restart local v8    # "e":Ljava/io/IOException;
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v9, -0x1

    return v9

    .end local v8    # "e":Ljava/io/IOException;
    :cond_31a
    :goto_31a
    throw v8

    .line 386
    .end local v7    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_31b
    :goto_31b
    const/4 v7, 0x2

    return v7

    .line 383
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    :cond_31d
    move-object v10, v8

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_3be

    .line 382
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    :cond_320
    move-object v10, v8

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_3be

    .line 533
    .end local v2    # "compareResult":I
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    :catchall_323
    move-exception v0

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    :catch_337
    move-exception v0

    move-object v7, v1

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    :catchall_345
    move-exception v0

    move-object/from16 v20, v10

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    :catch_35b
    move-exception v0

    move-object/from16 v20, v10

    move-object v7, v1

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    :catchall_36b
    move-exception v0

    move-object/from16 v20, v10

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :catch_380
    move-exception v0

    move-object/from16 v20, v10

    move-object v7, v1

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .local v7, "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :catchall_38f
    move-exception v0

    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :catch_3a6
    move-exception v0

    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object v7, v1

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 381
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "compareResult":I
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :cond_3b7
    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object/from16 v19, v11

    move-object v10, v8

    .line 387
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    :goto_3be
    const/4 v7, -0x1

    if-eq v2, v7, :cond_3c4

    const/4 v7, -0x3

    if-ne v2, v7, :cond_428

    .line 390
    :cond_3c4
    :try_start_3c4
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v7

    invoke-static {v7}, Landroid/os/Process;->killProcess(I)V

    .line 391
    const/4 v7, 0x0

    invoke-static {v7}, Ljava/lang/System;->exit(I)V
    :try_end_3cf
    .catch Ljava/lang/Exception; {:try_start_3c4 .. :try_end_3cf} :catch_3e5
    .catchall {:try_start_3c4 .. :try_end_3cf} :catchall_3d1

    goto/16 :goto_428

    .line 533
    .end local v2    # "compareResult":I
    :catchall_3d1
    move-exception v0

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    goto/16 :goto_1316

    .line 528
    :catch_3e5
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .local v2, "Cookiefile":Ljava/io/File;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :catchall_3f3
    move-exception v0

    move-object/from16 v18, v2

    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :catch_40c
    move-exception v0

    move-object/from16 v18, v2

    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object v7, v1

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 375
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .restart local v8    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .restart local v11    # "firstloadfilepath":Ljava/lang/String;
    :cond_41f
    move-object/from16 v18, v2

    move-object/from16 v17, v7

    move-object/from16 v20, v10

    move-object/from16 v19, v11

    move-object v10, v8

    .line 402
    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v8    # "Cookiefilepath":Ljava/lang/String;
    .end local v11    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    :cond_428
    :goto_428
    :try_start_428
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v7, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 404
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v7, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v2, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 408
    const/4 v2, -0x1

    .line 409
    .local v2, "deletedexresult":I
    const/4 v7, -0x1

    .line 410
    .local v7, "deletejarresult":I
    const/4 v8, -0x1

    .line 411
    .local v8, "deleteodexresult":I
    const/4 v11, -0x1

    .line 412
    .local v11, "deleteflagresult":I
    const/4 v15, 0x0

    .line 413
    .local v15, "file_count":I
    const/4 v15, 0x0

    :goto_45c
    move/from16 v21, v2

    .end local v2    # "deletedexresult":I
    .local v21, "deletedexresult":I
    sget v2, Lcom/wrapper/proxyapplication/Util;->MAX_DEX_NUM:I
    :try_end_460
    .catch Ljava/lang/Exception; {:try_start_428 .. :try_end_460} :catch_fe9
    .catchall {:try_start_428 .. :try_end_460} :catchall_fd5

    move/from16 v22, v7

    .end local v7    # "deletejarresult":I
    .local v22, "deletejarresult":I
    const-string v7, "/odexdir/"

    move/from16 v23, v8

    .end local v8    # "deleteodexresult":I
    .local v23, "deleteodexresult":I
    const-string v8, "/oat/arm64/"

    move/from16 v24, v11

    .end local v11    # "deleteflagresult":I
    .local v24, "deleteflagresult":I
    const-string v11, "/oat/arm/"

    move-object/from16 v25, v5

    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v25, "raf":Ljava/io/RandomAccessFile;
    const-string v5, "_"

    move-object/from16 v26, v3

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .local v26, "file_channel":Ljava/nio/channels/FileChannel;
    const-string v3, "."

    if-ge v15, v2, :cond_713

    .line 414
    :try_start_476
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_481
    .catch Ljava/lang/Exception; {:try_start_476 .. :try_end_481} :catch_6ff
    .catchall {:try_start_476 .. :try_end_481} :catchall_6ef

    move-object/from16 v27, v4

    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .local v27, "file_lock":Ljava/nio/channels/FileLock;
    :try_start_483
    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;
    :try_end_485
    .catch Ljava/lang/Exception; {:try_start_483 .. :try_end_485} :catch_6db
    .catchall {:try_start_483 .. :try_end_485} :catchall_6cd

    move-object/from16 v28, v14

    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v28, "Libnameinapk":Ljava/lang/String;
    :try_start_487
    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-static {v4, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v2, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v2

    .line 415
    .end local v21    # "deletedexresult":I
    .restart local v2    # "deletedexresult":I
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename1:Ljava/lang/String;
    :try_end_4b2
    .catch Ljava/lang/Exception; {:try_start_487 .. :try_end_4b2} :catch_6b8
    .catchall {:try_start_487 .. :try_end_4b2} :catchall_6a9

    move-object/from16 v29, v12

    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .local v29, "Cookiefileinzip":Ljava/lang/String;
    :try_start_4b4
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-static {v14, v3, v12}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v4, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-static {v4}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v4

    .line 416
    .end local v22    # "deletejarresult":I
    .local v4, "deletejarresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;
    :try_end_4df
    .catch Ljava/lang/Exception; {:try_start_4b4 .. :try_end_4df} :catch_693
    .catchall {:try_start_4b4 .. :try_end_4df} :catchall_683

    move-object/from16 v30, v10

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .local v30, "Cookiefilepath":Ljava/lang/String;
    :try_start_4e1
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    .line 417
    invoke-static {v14, v3, v10}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v10

    invoke-virtual {v12, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    .line 416
    invoke-static {v10}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v10

    .line 418
    .end local v23    # "deleteodexresult":I
    .local v10, "deleteodexresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename8:Ljava/lang/String;

    move-object/from16 v31, v7

    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 419
    invoke-static {v14, v3, v7}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 418
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v7

    .line 420
    .end local v24    # "deleteflagresult":I
    .local v7, "deleteflagresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    move/from16 v21, v7

    .end local v7    # "deleteflagresult":I
    .local v21, "deleteflagresult":I
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v14, v3, v7}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v7

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 421
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    .line 422
    invoke-static {v12, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 421
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 423
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-static {v12, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 424
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    invoke-static {v12, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 425
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    .line 426
    invoke-static {v12, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 425
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 427
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    .line 428
    invoke-static {v12, v3, v14}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 427
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 429
    const/4 v7, -0x1

    if-ne v7, v2, :cond_635

    if-ne v7, v4, :cond_635

    if-ne v7, v10, :cond_635

    .line 432
    move/from16 v23, v10

    goto/16 :goto_723

    .line 433
    :cond_635
    const/4 v3, -0x2

    if-eq v3, v2, :cond_63c

    if-eq v3, v4, :cond_63c

    if-ne v3, v10, :cond_647

    .line 435
    :cond_63c
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v3

    invoke-static {v3}, Landroid/os/Process;->killProcess(I)V

    .line 436
    const/4 v3, 0x0

    invoke-static {v3}, Ljava/lang/System;->exit(I)V
    :try_end_647
    .catch Ljava/lang/Exception; {:try_start_4e1 .. :try_end_647} :catch_66c
    .catchall {:try_start_4e1 .. :try_end_647} :catchall_65b

    .line 413
    :cond_647
    add-int/lit8 v15, v15, 0x1

    move v7, v4

    move v8, v10

    move/from16 v11, v21

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v14, v28

    move-object/from16 v12, v29

    move-object/from16 v10, v30

    goto/16 :goto_45c

    .line 533
    .end local v2    # "deletedexresult":I
    .end local v4    # "deletejarresult":I
    .end local v10    # "deleteodexresult":I
    .end local v15    # "file_count":I
    .end local v21    # "deleteflagresult":I
    :catchall_65b
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object v1, v0

    goto/16 :goto_1316

    .line 528
    :catch_66c
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object v1, v0

    goto/16 :goto_1100

    .line 533
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    :catchall_683
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v12, v10

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object v1, v0

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    :catch_693
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v12, v10

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object v1, v0

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catchall_6a9
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catch_6b8
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_6cd
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_6db
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v4, "file_lock":Ljava/nio/channels/FileLock;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_6ef
    move-exception v0

    move-object/from16 v27, v4

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_6ff
    move-exception v0

    move-object/from16 v27, v4

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object v1, v0

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 413
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v15    # "file_count":I
    .local v21, "deletedexresult":I
    .restart local v22    # "deletejarresult":I
    .restart local v23    # "deleteodexresult":I
    .restart local v24    # "deleteflagresult":I
    :cond_713
    move-object/from16 v27, v4

    move-object/from16 v31, v7

    move-object/from16 v30, v10

    move-object/from16 v29, v12

    move-object/from16 v28, v14

    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    move/from16 v2, v21

    move/from16 v4, v22

    move/from16 v21, v24

    .line 442
    .end local v22    # "deletejarresult":I
    .end local v24    # "deleteflagresult":I
    .restart local v2    # "deletedexresult":I
    .local v4, "deletejarresult":I
    .local v21, "deleteflagresult":I
    :goto_723
    :try_start_723
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename9:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 443
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 451
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v10, Ljava/io/File;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename5:Ljava/lang/String;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-direct {v10, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v7, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 453
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v10, Ljava/io/File;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-direct {v10, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v7, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 454
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v10, Ljava/io/File;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-direct {v10, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v7, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 455
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v13}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v10, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    new-instance v10, Ljava/io/File;

    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    invoke-direct {v10, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v1, v7, v10}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 459
    new-instance v7, Ljava/io/File;
    :try_end_807
    .catch Ljava/lang/Exception; {:try_start_723 .. :try_end_807} :catch_fbe
    .catchall {:try_start_723 .. :try_end_807} :catchall_fad

    move-object/from16 v10, v30

    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    :try_start_809
    invoke-direct {v7, v10}, Ljava/io/File;-><init>(Ljava/lang/String;)V
    :try_end_80c
    .catch Ljava/lang/Exception; {:try_start_809 .. :try_end_80c} :catch_f97
    .catchall {:try_start_809 .. :try_end_80c} :catchall_f87

    move-object/from16 v12, v29

    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :try_start_80e
    invoke-static {v1, v12, v7}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z
    :try_end_811
    .catch Ljava/lang/Exception; {:try_start_80e .. :try_end_811} :catch_f72
    .catchall {:try_start_80e .. :try_end_811} :catchall_f63

    .line 460
    const/4 v7, 0x0

    .local v7, "file_count2":I
    :goto_812
    if-ge v7, v15, :cond_af9

    .line 461
    :try_start_814
    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V
    :try_end_819
    .catch Ljava/lang/Exception; {:try_start_814 .. :try_end_819} :catch_ae3
    .catchall {:try_start_814 .. :try_end_819} :catchall_ad3

    move/from16 v22, v2

    move-object/from16 v2, v20

    .end local v20    # "backupfilepath":Ljava/lang/String;
    .local v2, "backupfilepath":Ljava/lang/String;
    .local v22, "deletedexresult":I
    :try_start_81d
    invoke-virtual {v14, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move/from16 v20, v4

    .end local v4    # "deletejarresult":I
    .local v20, "deletejarresult":I
    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;
    :try_end_827
    .catch Ljava/lang/Exception; {:try_start_81d .. :try_end_827} :catch_abd
    .catchall {:try_start_81d .. :try_end_827} :catchall_aad

    move-object/from16 v30, v10

    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    :try_start_829
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    .line 462
    invoke-static {v4, v3, v10}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v14, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    .line 461
    invoke-static {v4}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v4

    .line 463
    .end local v22    # "deletedexresult":I
    .local v4, "deletedexresult":I
    new-instance v10, Ljava/lang/StringBuilder;

    invoke-direct {v10}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v10, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename1:Ljava/lang/String;
    :try_end_854
    .catch Ljava/lang/Exception; {:try_start_829 .. :try_end_854} :catch_a96
    .catchall {:try_start_829 .. :try_end_854} :catchall_a85

    move-object/from16 v29, v12

    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    :try_start_856
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v12

    .line 464
    invoke-static {v14, v3, v12}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v10, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v10}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v10

    .line 463
    invoke-static {v10}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v10

    .line 465
    .end local v20    # "deletejarresult":I
    .local v10, "deletejarresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v14, v31

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;
    :try_end_881
    .catch Ljava/lang/Exception; {:try_start_856 .. :try_end_881} :catch_a6d
    .catchall {:try_start_856 .. :try_end_881} :catchall_a5b

    move-object/from16 v24, v6

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .local v24, "Appfiledir":Ljava/lang/String;
    :try_start_883
    sget-object v6, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;

    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 466
    invoke-static {v6, v3, v1}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v12, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 465
    invoke-static {v1}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v1

    .line 467
    .end local v23    # "deleteodexresult":I
    .local v1, "deleteodexresult":I
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename8:Ljava/lang/String;
    :try_end_8b0
    .catch Ljava/lang/Exception; {:try_start_883 .. :try_end_8b0} :catch_a42
    .catchall {:try_start_883 .. :try_end_8b0} :catchall_a2f

    move-object/from16 v31, v13

    :try_start_8b2
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 468
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 467
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v6

    move/from16 v21, v6

    .line 469
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 470
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 469
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 471
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 472
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 471
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 473
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 474
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 473
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 475
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 476
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 475
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 477
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 478
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 477
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 479
    new-instance v6, Ljava/lang/StringBuilder;

    invoke-direct {v6}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v6, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v7}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 480
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v6, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v6}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v6

    .line 479
    invoke-static {v6}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 481
    const/4 v6, -0x1

    if-ne v6, v4, :cond_9d9

    if-ne v6, v10, :cond_9d9

    if-ne v6, v1, :cond_9d9

    .line 484
    move/from16 v20, v10

    goto/16 :goto_b0d

    .line 485
    :cond_9d9
    const/4 v6, -0x2

    if-eq v6, v4, :cond_9e0

    if-eq v6, v10, :cond_9e0

    if-ne v6, v1, :cond_9eb

    .line 487
    :cond_9e0
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v6

    invoke-static {v6}, Landroid/os/Process;->killProcess(I)V

    .line 488
    const/4 v6, 0x0

    invoke-static {v6}, Ljava/lang/System;->exit(I)V
    :try_end_9eb
    .catch Ljava/lang/Exception; {:try_start_8b2 .. :try_end_9eb} :catch_a15
    .catchall {:try_start_8b2 .. :try_end_9eb} :catchall_a01

    .line 460
    :cond_9eb
    add-int/lit8 v7, v7, 0x1

    move/from16 v23, v1

    move-object/from16 v20, v2

    move v2, v4

    move v4, v10

    move-object/from16 v6, v24

    move-object/from16 v12, v29

    move-object/from16 v10, v30

    move-object/from16 v13, v31

    move-object/from16 v1, p1

    move-object/from16 v31, v14

    goto/16 :goto_812

    .line 533
    .end local v1    # "deleteodexresult":I
    .end local v4    # "deletedexresult":I
    .end local v7    # "file_count2":I
    .end local v10    # "deletejarresult":I
    .end local v15    # "file_count":I
    .end local v21    # "deleteflagresult":I
    :catchall_a01
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object/from16 v10, v19

    move-object/from16 v8, v24

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    goto/16 :goto_1316

    .line 528
    :catch_a15
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object/from16 v10, v19

    move-object/from16 v8, v24

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    goto/16 :goto_1100

    .line 533
    :catchall_a2f
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v8, v24

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    goto/16 :goto_1316

    .line 528
    :catch_a42
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v8, v24

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    goto/16 :goto_1100

    .line 533
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    :catchall_a5b
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    :catch_a6d
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catchall_a85
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v6, v28

    move-object/from16 v12, v30

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catch_a96
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v12, v30

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catchall_aad
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    :catch_abd
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v22, v2

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v2    # "backupfilepath":Ljava/lang/String;
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .local v20, "backupfilepath":Ljava/lang/String;
    :catchall_ad3
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v2    # "backupfilepath":Ljava/lang/String;
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    :catch_ae3
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 460
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v2, "deletedexresult":I
    .local v4, "deletejarresult":I
    .restart local v6    # "Appfiledir":Ljava/lang/String;
    .restart local v7    # "file_count2":I
    .restart local v10    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v15    # "file_count":I
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v21    # "deleteflagresult":I
    .restart local v23    # "deleteodexresult":I
    :cond_af9
    move/from16 v22, v2

    move-object/from16 v24, v6

    move-object/from16 v30, v10

    move-object/from16 v29, v12

    move-object/from16 v2, v20

    move-object/from16 v14, v31

    move/from16 v20, v4

    move-object/from16 v31, v13

    .end local v4    # "deletejarresult":I
    .end local v6    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "Cookiefilepath":Ljava/lang/String;
    .end local v12    # "Cookiefileinzip":Ljava/lang/String;
    .local v2, "backupfilepath":Ljava/lang/String;
    .local v20, "deletejarresult":I
    .restart local v22    # "deletedexresult":I
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    move/from16 v4, v22

    move/from16 v1, v23

    .line 493
    .end local v7    # "file_count2":I
    .end local v22    # "deletedexresult":I
    .end local v23    # "deleteodexresult":I
    .restart local v1    # "deleteodexresult":I
    .local v4, "deletedexresult":I
    :goto_b0d
    const/4 v6, 0x0

    .local v6, "file_count3":I
    :goto_b0e
    if-ge v6, v15, :cond_d64

    .line 494
    :try_start_b10
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V
    :try_end_b15
    .catch Ljava/lang/Exception; {:try_start_b10 .. :try_end_b15} :catch_d4a
    .catchall {:try_start_b10 .. :try_end_b15} :catchall_d36

    move-object/from16 v10, v19

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    :try_start_b17
    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 495
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 494
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v7

    move v4, v7

    .line 496
    new-instance v7, Ljava/lang/StringBuilder;

    invoke-direct {v7}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v7, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename1:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 497
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v7, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v7}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v7

    .line 496
    invoke-static {v7}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v7

    .line 498
    .end local v20    # "deletejarresult":I
    .local v7, "deletejarresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v13, Lcom/wrapper/proxyapplication/Util;->securename0:Ljava/lang/String;

    move/from16 v19, v1

    .end local v1    # "deleteodexresult":I
    .local v19, "deleteodexresult":I
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 499
    invoke-static {v13, v3, v1}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v12, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    .line 498
    invoke-static {v1}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v1

    .line 500
    .end local v19    # "deleteodexresult":I
    .restart local v1    # "deleteodexresult":I
    new-instance v12, Ljava/lang/StringBuilder;

    invoke-direct {v12}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v12, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v13, Lcom/wrapper/proxyapplication/Util;->securename8:Ljava/lang/String;
    :try_end_ba3
    .catch Ljava/lang/Exception; {:try_start_b17 .. :try_end_ba3} :catch_d1e
    .catchall {:try_start_b17 .. :try_end_ba3} :catchall_d0c

    move-object/from16 v22, v2

    .end local v2    # "backupfilepath":Ljava/lang/String;
    .local v22, "backupfilepath":Ljava/lang/String;
    :try_start_ba5
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 501
    invoke-static {v13, v3, v2}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v2

    invoke-virtual {v12, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v12}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 500
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    move-result v2

    move/from16 v21, v2

    .line 502
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 503
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 502
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 504
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename11:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 505
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 504
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 506
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 507
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 506
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 508
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 509
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 508
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 510
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename14:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 511
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 510
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 512
    new-instance v2, Ljava/lang/StringBuilder;

    invoke-direct {v2}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v2, v10}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v12, Lcom/wrapper/proxyapplication/Util;->securename15:Ljava/lang/String;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 513
    invoke-static {v12, v3, v13}, Lcom/wrapper/proxyapplication/Util;->CreatenewFileName(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;

    move-result-object v12

    invoke-virtual {v2, v12}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v2}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    .line 512
    invoke-static {v2}, Lcom/wrapper/proxyapplication/Util;->DeleteFile(Ljava/lang/String;)I

    .line 514
    const/4 v2, -0x1

    if-ne v2, v4, :cond_cca

    if-ne v2, v7, :cond_cca

    if-ne v2, v1, :cond_cca

    .line 517
    goto/16 :goto_d6a

    .line 518
    :cond_cca
    const/4 v2, -0x2

    if-eq v2, v4, :cond_cd1

    if-eq v2, v7, :cond_cd1

    if-ne v2, v1, :cond_cdc

    .line 520
    :cond_cd1
    invoke-static {}, Landroid/os/Process;->myPid()I

    move-result v12

    invoke-static {v12}, Landroid/os/Process;->killProcess(I)V

    .line 521
    const/4 v12, 0x0

    invoke-static {v12}, Ljava/lang/System;->exit(I)V
    :try_end_cdc
    .catch Ljava/lang/Exception; {:try_start_ba5 .. :try_end_cdc} :catch_cf6
    .catchall {:try_start_ba5 .. :try_end_cdc} :catchall_ce6

    .line 493
    :cond_cdc
    add-int/lit8 v6, v6, 0x1

    move/from16 v20, v7

    move-object/from16 v19, v10

    move-object/from16 v2, v22

    goto/16 :goto_b0e

    .line 533
    .end local v1    # "deleteodexresult":I
    .end local v4    # "deletedexresult":I
    .end local v6    # "file_count3":I
    .end local v7    # "deletejarresult":I
    .end local v15    # "file_count":I
    .end local v21    # "deleteflagresult":I
    :catchall_ce6
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    goto/16 :goto_1316

    .line 528
    :catch_cf6
    move-exception v0

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    goto/16 :goto_1100

    .line 533
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    :catchall_d0c
    move-exception v0

    move-object/from16 v22, v2

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    .end local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    :catch_d1e
    move-exception v0

    move-object/from16 v22, v2

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    .end local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v10    # "firstloadfilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    .local v19, "firstloadfilepath":Ljava/lang/String;
    :catchall_d36
    move-exception v0

    move-object/from16 v22, v2

    move-object/from16 v10, v19

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    .end local v2    # "backupfilepath":Ljava/lang/String;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v10    # "firstloadfilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v10    # "firstloadfilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    :catch_d4a
    move-exception v0

    move-object/from16 v22, v2

    move-object/from16 v10, v19

    move-object/from16 v7, p1

    move-object v1, v0

    move-object/from16 v8, v24

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object/from16 v2, v31

    .end local v2    # "backupfilepath":Ljava/lang/String;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v10    # "firstloadfilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 493
    .end local v10    # "firstloadfilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v1    # "deleteodexresult":I
    .restart local v2    # "backupfilepath":Ljava/lang/String;
    .restart local v4    # "deletedexresult":I
    .restart local v6    # "file_count3":I
    .restart local v15    # "file_count":I
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "deletejarresult":I
    .restart local v21    # "deleteflagresult":I
    :cond_d64
    move-object/from16 v22, v2

    move-object/from16 v10, v19

    move/from16 v19, v1

    .line 533
    .end local v1    # "deleteodexresult":I
    .end local v2    # "backupfilepath":Ljava/lang/String;
    .end local v4    # "deletedexresult":I
    .end local v6    # "file_count3":I
    .end local v15    # "file_count":I
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "deletejarresult":I
    .end local v21    # "deleteflagresult":I
    .restart local v10    # "firstloadfilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    :goto_d6a
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v2, v31

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v6, v28

    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    move-object/from16 v7, p1

    invoke-virtual {v7, v1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 534
    .local v1, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v1, :cond_dd1

    .line 535
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    move-object/from16 v8, v24

    .end local v24    # "Appfiledir":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v4

    invoke-static {v3, v4, v5}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_dd3

    .line 536
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/io/File;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v11, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v5, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v3, v4}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_dd3

    .line 534
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .restart local v24    # "Appfiledir":Ljava/lang/String;
    :cond_dd1
    move-object/from16 v8, v24

    .line 539
    .end local v24    # "Appfiledir":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    :cond_dd3
    :goto_dd3
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v3}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 540
    if-eqz v1, :cond_e33

    .line 541
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v4

    invoke-static {v3, v4, v5}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_e33

    .line 542
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/io/File;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v11, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v5, v11}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v3, v4}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 545
    :cond_e33
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v3}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 546
    if-eqz v1, :cond_e93

    .line 547
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v4

    invoke-static {v3, v4, v5}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_e93

    .line 548
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/io/File;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v3, v4}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v2, v3}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 551
    :cond_e93
    move-object/from16 v11, v29

    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    invoke-virtual {v7, v11}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 552
    if-eqz v1, :cond_eb0

    .line 553
    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v2

    move-object/from16 v12, v30

    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    invoke-static {v12, v2, v3}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v2

    if-nez v2, :cond_eb2

    .line 554
    new-instance v2, Ljava/io/File;

    invoke-direct {v2, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v11, v2}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_eb2

    .line 552
    .end local v12    # "Cookiefilepath":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    :cond_eb0
    move-object/from16 v12, v30

    .line 557
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .restart local v12    # "Cookiefilepath":Ljava/lang/String;
    :cond_eb2
    :goto_eb2
    if-eqz v27, :cond_f60

    .line 559
    :try_start_eb4
    invoke-virtual/range {v27 .. v27}, Ljava/nio/channels/FileLock;->release()V
    :try_end_eb7
    .catch Ljava/io/IOException; {:try_start_eb4 .. :try_end_eb7} :catch_ef0
    .catchall {:try_start_eb4 .. :try_end_eb7} :catchall_eed

    .line 565
    if-eqz v26, :cond_f60

    .line 567
    :try_start_eb9
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_ebc
    .catch Ljava/io/IOException; {:try_start_eb9 .. :try_end_ebc} :catch_ecd
    .catchall {:try_start_eb9 .. :try_end_ebc} :catchall_eca

    .line 573
    nop

    .line 575
    :try_start_ebd
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_ec0
    .catch Ljava/io/IOException; {:try_start_ebd .. :try_end_ec0} :catch_ec3

    .line 580
    nop

    .line 579
    goto/16 :goto_f60

    .line 576
    :catch_ec3
    move-exception v0

    move-object v2, v0

    .line 578
    .local v2, "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .line 573
    .end local v2    # "e":Ljava/io/IOException;
    :catchall_eca
    move-exception v0

    move-object v2, v0

    goto :goto_ee1

    .line 568
    :catch_ecd
    move-exception v0

    move-object v2, v0

    .line 570
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_ecf
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_ed2
    .catchall {:try_start_ecf .. :try_end_ed2} :catchall_eca

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_ed4
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_ed7
    .catch Ljava/io/IOException; {:try_start_ed4 .. :try_end_ed7} :catch_eda

    .line 580
    :goto_ed7
    nop

    .line 571
    const/4 v3, -0x1

    return v3

    .line 576
    :catch_eda
    move-exception v0

    const/4 v3, -0x1

    move-object v4, v0

    .line 578
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v3

    .line 575
    .end local v2    # "e":Ljava/io/IOException;
    .end local v4    # "e":Ljava/io/IOException;
    :goto_ee1
    :try_start_ee1
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_ee4
    .catch Ljava/io/IOException; {:try_start_ee1 .. :try_end_ee4} :catch_ee6

    .line 580
    nop

    .line 579
    throw v2

    .line 576
    :catch_ee6
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .line 565
    .end local v2    # "e":Ljava/io/IOException;
    :catchall_eed
    move-exception v0

    move-object v2, v0

    goto :goto_f2d

    .line 560
    :catch_ef0
    move-exception v0

    move-object v2, v0

    .line 562
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_ef2
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_ef5
    .catchall {:try_start_ef2 .. :try_end_ef5} :catchall_eed

    .line 563
    nop

    .line 565
    if-eqz v26, :cond_f2b

    .line 567
    :try_start_ef8
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_efb
    .catch Ljava/io/IOException; {:try_start_ef8 .. :try_end_efb} :catch_f0b
    .catchall {:try_start_ef8 .. :try_end_efb} :catchall_f08

    .line 573
    nop

    .line 575
    :try_start_efc
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_eff
    .catch Ljava/io/IOException; {:try_start_efc .. :try_end_eff} :catch_f01

    .line 580
    const/4 v4, -0x1

    goto :goto_f2c

    .line 576
    :catch_f01
    move-exception v0

    move-object v3, v0

    .line 578
    .local v3, "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :catchall_f08
    move-exception v0

    move-object v3, v0

    goto :goto_f1f

    .line 568
    :catch_f0b
    move-exception v0

    move-object v3, v0

    .line 570
    .restart local v3    # "e":Ljava/io/IOException;
    :try_start_f0d
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V
    :try_end_f10
    .catchall {:try_start_f0d .. :try_end_f10} :catchall_f08

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_f12
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_f15
    .catch Ljava/io/IOException; {:try_start_f12 .. :try_end_f15} :catch_f18

    .line 580
    nop

    .line 571
    const/4 v4, -0x1

    return v4

    .line 576
    :catch_f18
    move-exception v0

    const/4 v4, -0x1

    move-object v5, v0

    .line 578
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v4

    .line 575
    .end local v3    # "e":Ljava/io/IOException;
    .end local v5    # "e":Ljava/io/IOException;
    :goto_f1f
    :try_start_f1f
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_f22
    .catch Ljava/io/IOException; {:try_start_f1f .. :try_end_f22} :catch_f24

    .line 580
    nop

    .line 579
    throw v3

    .line 576
    :catch_f24
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 565
    .end local v3    # "e":Ljava/io/IOException;
    :cond_f2b
    const/4 v4, -0x1

    .line 563
    :goto_f2c
    return v4

    .line 565
    .end local v2    # "e":Ljava/io/IOException;
    :goto_f2d
    if-eqz v26, :cond_f5f

    .line 567
    :try_start_f2f
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_f32
    .catch Ljava/io/IOException; {:try_start_f2f .. :try_end_f32} :catch_f41
    .catchall {:try_start_f2f .. :try_end_f32} :catchall_f3e

    .line 573
    nop

    .line 575
    :try_start_f33
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_f36
    .catch Ljava/io/IOException; {:try_start_f33 .. :try_end_f36} :catch_f37

    .line 580
    goto :goto_f5f

    .line 576
    :catch_f37
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .line 573
    .end local v2    # "e":Ljava/io/IOException;
    :catchall_f3e
    move-exception v0

    move-object v2, v0

    goto :goto_f53

    .line 568
    :catch_f41
    move-exception v0

    move-object v2, v0

    .line 570
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_f43
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_f46
    .catchall {:try_start_f43 .. :try_end_f46} :catchall_f3e

    .line 571
    nop

    .line 573
    nop

    .line 575
    :try_start_f48
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_f4b
    .catch Ljava/io/IOException; {:try_start_f48 .. :try_end_f4b} :catch_f4c

    goto :goto_ed7

    .line 576
    :catch_f4c
    move-exception v0

    const/4 v3, -0x1

    move-object v4, v0

    .line 578
    .restart local v4    # "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    return v3

    .line 575
    .end local v2    # "e":Ljava/io/IOException;
    .end local v4    # "e":Ljava/io/IOException;
    :goto_f53
    :try_start_f53
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_f56
    .catch Ljava/io/IOException; {:try_start_f53 .. :try_end_f56} :catch_f58

    .line 580
    nop

    .line 579
    throw v2

    .line 576
    :catch_f58
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v2    # "e":Ljava/io/IOException;
    :cond_f5f
    :goto_f5f
    throw v2

    .line 586
    .end local v1    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_f60
    :goto_f60
    nop

    .line 587
    const/4 v1, 0x0

    return v1

    .line 533
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .local v20, "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    :catchall_f63
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    move-object v1, v0

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    :catch_f72
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object v12, v10

    move-object/from16 v10, v19

    move-object v1, v0

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v12    # "Cookiefilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    :catchall_f87
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v12, v10

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object v1, v0

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v12    # "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v12    # "Cookiefilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    :catch_f97
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v12, v10

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object v1, v0

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v12    # "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "firstloadfilepath":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v12    # "Cookiefilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    :catchall_fad
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object v1, v0

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .restart local v10    # "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v12    # "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v10    # "firstloadfilepath":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v12    # "Cookiefilepath":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    .restart local v28    # "Libnameinapk":Ljava/lang/String;
    .restart local v29    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v30    # "Cookiefilepath":Ljava/lang/String;
    :catch_fbe
    move-exception v0

    move-object v7, v1

    move-object v8, v6

    move-object v2, v13

    move-object/from16 v10, v19

    move-object/from16 v22, v20

    move-object/from16 v6, v28

    move-object/from16 v11, v29

    move-object/from16 v12, v30

    move-object v1, v0

    move-object/from16 v5, v25

    move-object/from16 v3, v26

    move-object/from16 v4, v27

    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .end local v28    # "Libnameinapk":Ljava/lang/String;
    .end local v29    # "Cookiefileinzip":Ljava/lang/String;
    .end local v30    # "Cookiefilepath":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .restart local v10    # "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .restart local v12    # "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1100

    .line 533
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .local v3, "file_channel":Ljava/nio/channels/FileChannel;
    .local v4, "file_lock":Ljava/nio/channels/FileLock;
    .local v5, "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    :catchall_fd5
    move-exception v0

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v12, v10

    move-object/from16 v10, v19

    move-object v1, v0

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1316

    .line 528
    .end local v8    # "Appfiledir":Ljava/lang/String;
    .end local v11    # "Cookiefileinzip":Ljava/lang/String;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v10, "Cookiefilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v19    # "firstloadfilepath":Ljava/lang/String;
    .restart local v20    # "backupfilepath":Ljava/lang/String;
    :catch_fe9
    move-exception v0

    move-object v7, v1

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object v8, v6

    move-object v11, v12

    move-object v2, v13

    move-object v6, v14

    move-object/from16 v22, v20

    move-object v12, v10

    move-object/from16 v10, v19

    move-object v1, v0

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .end local v19    # "firstloadfilepath":Ljava/lang/String;
    .end local v20    # "backupfilepath":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .restart local v8    # "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .restart local v11    # "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .local v2, "Cookiefile":Ljava/io/File;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .local v7, "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_ffd
    move-exception v0

    move-object/from16 v18, v2

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_1014
    move-exception v0

    move-object/from16 v18, v2

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v18    # "Cookiefile":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    .restart local v16    # "Cookiefile":Ljava/io/File;
    :catchall_102b
    move-exception v0

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_1042
    move-exception v0

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v27    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_1059
    move-exception v0

    move-object/from16 v26, v3

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v27, v4

    move-object/from16 v18, v16

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_1070
    move-exception v0

    move-object/from16 v26, v3

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .end local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_1085
    move-exception v0

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v18, v16

    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_109c
    move-exception v0

    move-object/from16 v25, v5

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    goto/16 :goto_1100

    .line 533
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .end local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v5    # "raf":Ljava/io/RandomAccessFile;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_10af
    move-exception v0

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v18, v16

    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_10c6
    move-exception v0

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto :goto_1100

    .line 533
    .end local v16    # "Cookiefile":Ljava/io/File;
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catchall_10d6
    move-exception v0

    move-object/from16 v16, v2

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v26, v3

    move-object/from16 v27, v4

    move-object/from16 v25, v5

    move-object/from16 v18, v16

    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v16    # "Cookiefile":Ljava/io/File;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    goto/16 :goto_1316

    .line 528
    .end local v16    # "Cookiefile":Ljava/io/File;
    .end local v17    # "Appprofiledir":Ljava/io/File;
    .end local v22    # "backupfilepath":Ljava/lang/String;
    .restart local v2    # "Cookiefile":Ljava/io/File;
    .local v6, "Appfiledir":Ljava/lang/String;
    .restart local v7    # "Appprofiledir":Ljava/io/File;
    .local v8, "Cookiefilepath":Ljava/lang/String;
    .local v10, "backupfilepath":Ljava/lang/String;
    .local v11, "firstloadfilepath":Ljava/lang/String;
    .local v12, "Cookiefileinzip":Ljava/lang/String;
    .restart local v14    # "Libnameinapk":Ljava/lang/String;
    :catch_10ef
    move-exception v0

    move-object/from16 v16, v2

    move-object/from16 v17, v7

    move-object/from16 v22, v10

    move-object v10, v11

    move-object v11, v12

    move-object v2, v13

    move-object v7, v1

    move-object v12, v8

    move-object v8, v6

    move-object v6, v14

    move-object v1, v0

    move-object/from16 v18, v16

    .line 530
    .end local v2    # "Cookiefile":Ljava/io/File;
    .end local v7    # "Appprofiledir":Ljava/io/File;
    .end local v14    # "Libnameinapk":Ljava/lang/String;
    .local v1, "e":Ljava/lang/Exception;
    .local v6, "Libnameinapk":Ljava/lang/String;
    .local v8, "Appfiledir":Ljava/lang/String;
    .local v10, "firstloadfilepath":Ljava/lang/String;
    .local v11, "Cookiefileinzip":Ljava/lang/String;
    .local v12, "Cookiefilepath":Ljava/lang/String;
    .restart local v17    # "Appprofiledir":Ljava/io/File;
    .restart local v18    # "Cookiefile":Ljava/io/File;
    .restart local v22    # "backupfilepath":Ljava/lang/String;
    :goto_1100
    :try_start_1100
    invoke-virtual {v1}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_1103
    .catchall {:try_start_1100 .. :try_end_1103} :catchall_130b

    .line 531
    nop

    .line 533
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-virtual {v7, v13}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v13

    .line 534
    .local v13, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v13, :cond_116b

    .line 535
    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v15, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v14, v15}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v14

    move-object/from16 v16, v3

    move-object v15, v4

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .local v15, "file_lock":Ljava/nio/channels/FileLock;
    .local v16, "file_channel":Ljava/nio/channels/FileChannel;
    invoke-virtual {v13}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v3

    invoke-static {v14, v3, v4}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_1168

    .line 536
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/io/File;

    new-instance v14, Ljava/lang/StringBuilder;

    invoke-direct {v14}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v14, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-object/from16 v19, v1

    .end local v1    # "e":Ljava/lang/Exception;
    .local v19, "e":Ljava/lang/Exception;
    sget-object v1, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v14, v1}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v14}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-direct {v4, v1}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v3, v4}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    goto :goto_1170

    .line 535
    .end local v19    # "e":Ljava/lang/Exception;
    .restart local v1    # "e":Ljava/lang/Exception;
    :cond_1168
    move-object/from16 v19, v1

    .end local v1    # "e":Ljava/lang/Exception;
    .restart local v19    # "e":Ljava/lang/Exception;
    goto :goto_1170

    .line 534
    .end local v15    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v16    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v19    # "e":Ljava/lang/Exception;
    .restart local v1    # "e":Ljava/lang/Exception;
    .restart local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v4    # "file_lock":Ljava/nio/channels/FileLock;
    :cond_116b
    move-object/from16 v19, v1

    move-object/from16 v16, v3

    move-object v15, v4

    .line 539
    .end local v1    # "e":Ljava/lang/Exception;
    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v15    # "file_lock":Ljava/nio/channels/FileLock;
    .restart local v16    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v19    # "e":Ljava/lang/Exception;
    :goto_1170
    new-instance v1, Ljava/lang/StringBuilder;

    invoke-direct {v1}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v1, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v1}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v1

    invoke-virtual {v7, v1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 540
    .end local v13    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .local v1, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v1, :cond_11d0

    .line 541
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v13

    invoke-static {v3, v13, v14}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_11d0

    .line 542
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    new-instance v4, Ljava/io/File;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-direct {v4, v13}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v3, v4}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 545
    :cond_11d0
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v3}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 546
    if-eqz v1, :cond_1230

    .line 547
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v4, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v3, v4}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v13

    invoke-static {v3, v13, v14}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_1230

    .line 548
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-instance v3, Ljava/io/File;

    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v9, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-direct {v3, v4}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v2, v3}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 551
    :cond_1230
    invoke-virtual {v7, v11}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v1

    .line 552
    if-eqz v1, :cond_1248

    .line 553
    invoke-virtual {v1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v2

    invoke-static {v12, v2, v3}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v2

    if-nez v2, :cond_1248

    .line 554
    new-instance v2, Ljava/io/File;

    invoke-direct {v2, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v11, v2}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 557
    :cond_1248
    if-eqz v15, :cond_1309

    .line 559
    :try_start_124a
    invoke-virtual {v15}, Ljava/nio/channels/FileLock;->release()V
    :try_end_124d
    .catch Ljava/io/IOException; {:try_start_124a .. :try_end_124d} :catch_128b
    .catchall {:try_start_124a .. :try_end_124d} :catchall_1288

    .line 565
    if-eqz v16, :cond_1309

    .line 567
    :try_start_124f
    invoke-virtual/range {v16 .. v16}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_1252
    .catch Ljava/io/IOException; {:try_start_124f .. :try_end_1252} :catch_1264
    .catchall {:try_start_124f .. :try_end_1252} :catchall_1261

    .line 573
    if-eqz v5, :cond_125f

    .line 575
    :try_start_1254
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_1257
    .catch Ljava/io/IOException; {:try_start_1254 .. :try_end_1257} :catch_1258

    .line 580
    goto :goto_125f

    .line 576
    :catch_1258
    move-exception v0

    move-object v2, v0

    .line 578
    .local v2, "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v2    # "e":Ljava/io/IOException;
    :cond_125f
    :goto_125f
    goto/16 :goto_1309

    .line 573
    :catchall_1261
    move-exception v0

    move-object v2, v0

    goto :goto_127a

    .line 568
    :catch_1264
    move-exception v0

    move-object v2, v0

    .line 570
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_1266
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_1269
    .catchall {:try_start_1266 .. :try_end_1269} :catchall_1261

    .line 571
    nop

    .line 573
    if-eqz v5, :cond_1278

    .line 575
    :try_start_126c
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_126f
    .catch Ljava/io/IOException; {:try_start_126c .. :try_end_126f} :catch_1271

    .line 580
    const/4 v4, -0x1

    goto :goto_1279

    .line 576
    :catch_1271
    move-exception v0

    move-object v3, v0

    .line 578
    .local v3, "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_1278
    const/4 v4, -0x1

    .line 571
    :goto_1279
    return v4

    .line 573
    .end local v2    # "e":Ljava/io/IOException;
    :goto_127a
    if-eqz v5, :cond_1287

    .line 575
    :try_start_127c
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_127f
    .catch Ljava/io/IOException; {:try_start_127c .. :try_end_127f} :catch_1280

    .line 580
    goto :goto_1287

    .line 576
    :catch_1280
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v2    # "e":Ljava/io/IOException;
    :cond_1287
    :goto_1287
    throw v2

    .line 565
    :catchall_1288
    move-exception v0

    move-object v2, v0

    goto :goto_12cf

    .line 560
    :catch_128b
    move-exception v0

    move-object v2, v0

    .line 562
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_128d
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_1290
    .catchall {:try_start_128d .. :try_end_1290} :catchall_1288

    .line 563
    nop

    .line 565
    if-eqz v16, :cond_12cd

    .line 567
    :try_start_1293
    invoke-virtual/range {v16 .. v16}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_1296
    .catch Ljava/io/IOException; {:try_start_1293 .. :try_end_1296} :catch_12a9
    .catchall {:try_start_1293 .. :try_end_1296} :catchall_12a6

    .line 573
    if-eqz v5, :cond_12a4

    .line 575
    :try_start_1298
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_129b
    .catch Ljava/io/IOException; {:try_start_1298 .. :try_end_129b} :catch_129d

    .line 580
    const/4 v4, -0x1

    goto :goto_12ce

    .line 576
    :catch_129d
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_12a4
    const/4 v4, -0x1

    goto :goto_12ce

    :catchall_12a6
    move-exception v0

    move-object v3, v0

    goto :goto_12bf

    .line 568
    :catch_12a9
    move-exception v0

    move-object v3, v0

    .line 570
    .restart local v3    # "e":Ljava/io/IOException;
    :try_start_12ab
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V
    :try_end_12ae
    .catchall {:try_start_12ab .. :try_end_12ae} :catchall_12a6

    .line 571
    nop

    .line 573
    if-eqz v5, :cond_12bd

    .line 575
    :try_start_12b1
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_12b4
    .catch Ljava/io/IOException; {:try_start_12b1 .. :try_end_12b4} :catch_12b6

    .line 580
    const/4 v9, -0x1

    goto :goto_12be

    .line 576
    :catch_12b6
    move-exception v0

    move-object v4, v0

    .line 578
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v9, -0x1

    return v9

    .line 573
    .end local v4    # "e":Ljava/io/IOException;
    :cond_12bd
    const/4 v9, -0x1

    .line 571
    :goto_12be
    return v9

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :goto_12bf
    if-eqz v5, :cond_12cc

    .line 575
    :try_start_12c1
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_12c4
    .catch Ljava/io/IOException; {:try_start_12c1 .. :try_end_12c4} :catch_12c5

    .line 580
    goto :goto_12cc

    .line 576
    :catch_12c5
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .end local v3    # "e":Ljava/io/IOException;
    :cond_12cc
    :goto_12cc
    throw v3

    .line 565
    :cond_12cd
    const/4 v4, -0x1

    .line 563
    :goto_12ce
    return v4

    .line 565
    .end local v2    # "e":Ljava/io/IOException;
    :goto_12cf
    if-eqz v16, :cond_1308

    .line 567
    :try_start_12d1
    invoke-virtual/range {v16 .. v16}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_12d4
    .catch Ljava/io/IOException; {:try_start_12d1 .. :try_end_12d4} :catch_12e4
    .catchall {:try_start_12d1 .. :try_end_12d4} :catchall_12e1

    .line 573
    if-eqz v5, :cond_1308

    .line 575
    :try_start_12d6
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_12d9
    .catch Ljava/io/IOException; {:try_start_12d6 .. :try_end_12d9} :catch_12da

    .line 580
    goto :goto_1308

    .line 576
    :catch_12da
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .line 573
    .end local v2    # "e":Ljava/io/IOException;
    :catchall_12e1
    move-exception v0

    move-object v2, v0

    goto :goto_12fa

    .line 568
    :catch_12e4
    move-exception v0

    move-object v2, v0

    .line 570
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_12e6
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_12e9
    .catchall {:try_start_12e6 .. :try_end_12e9} :catchall_12e1

    .line 571
    nop

    .line 573
    if-eqz v5, :cond_12f8

    .line 575
    :try_start_12ec
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_12ef
    .catch Ljava/io/IOException; {:try_start_12ec .. :try_end_12ef} :catch_12f1

    .line 580
    const/4 v4, -0x1

    goto :goto_12f9

    .line 576
    :catch_12f1
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_12f8
    const/4 v4, -0x1

    .line 571
    :goto_12f9
    return v4

    .line 573
    .end local v2    # "e":Ljava/io/IOException;
    :goto_12fa
    if-eqz v5, :cond_1307

    .line 575
    :try_start_12fc
    invoke-virtual {v5}, Ljava/io/RandomAccessFile;->close()V
    :try_end_12ff
    .catch Ljava/io/IOException; {:try_start_12fc .. :try_end_12ff} :catch_1300

    .line 580
    goto :goto_1307

    .line 576
    :catch_1300
    move-exception v0

    move-object v2, v0

    .line 578
    .restart local v2    # "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v2    # "e":Ljava/io/IOException;
    :cond_1307
    :goto_1307
    throw v2

    :cond_1308
    :goto_1308
    throw v2

    .line 531
    .end local v1    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_1309
    :goto_1309
    const/4 v1, -0x1

    return v1

    .line 533
    .end local v15    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v16    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v19    # "e":Ljava/lang/Exception;
    .local v3, "file_channel":Ljava/nio/channels/FileChannel;
    .local v4, "file_lock":Ljava/nio/channels/FileLock;
    :catchall_130b
    move-exception v0

    move-object/from16 v16, v3

    move-object v15, v4

    move-object v1, v0

    move-object/from16 v25, v5

    move-object/from16 v27, v15

    move-object/from16 v26, v16

    .end local v3    # "file_channel":Ljava/nio/channels/FileChannel;
    .end local v4    # "file_lock":Ljava/nio/channels/FileLock;
    .end local v5    # "raf":Ljava/io/RandomAccessFile;
    .restart local v25    # "raf":Ljava/io/RandomAccessFile;
    .restart local v26    # "file_channel":Ljava/nio/channels/FileChannel;
    .restart local v27    # "file_lock":Ljava/nio/channels/FileLock;
    :goto_1316
    new-instance v3, Ljava/lang/StringBuilder;

    invoke-direct {v3}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v3, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v3}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v3

    invoke-virtual {v7, v3}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v3

    .line 534
    .local v3, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v3, :cond_1374

    .line 535
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v13

    invoke-static {v4, v13, v14}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v4

    if-nez v4, :cond_1374

    .line 536
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    new-instance v5, Ljava/io/File;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->libname:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-direct {v5, v13}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v4, v5}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 539
    :cond_1374
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v7, v4}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v3

    .line 540
    if-eqz v3, :cond_13d4

    .line 541
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v13

    invoke-static {v4, v13, v14}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v4

    if-nez v4, :cond_13d4

    .line 542
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    new-instance v5, Ljava/io/File;

    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v13, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v14, Lcom/wrapper/proxyapplication/Util;->securename6:Ljava/lang/String;

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    invoke-direct {v5, v13}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v4, v5}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 545
    :cond_13d4
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v6}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v7, v4}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v3

    .line 546
    if-eqz v3, :cond_1434

    .line 547
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v5, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v4, v5}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v4

    invoke-virtual {v3}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v13

    invoke-static {v4, v13, v14}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v4

    if-nez v4, :cond_1434

    .line 548
    new-instance v4, Ljava/lang/StringBuilder;

    invoke-direct {v4}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v2, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v4, v2}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v4}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v2

    new-instance v4, Ljava/io/File;

    new-instance v5, Ljava/lang/StringBuilder;

    invoke-direct {v5}, Ljava/lang/StringBuilder;-><init>()V

    invoke-virtual {v5, v8}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    sget-object v9, Lcom/wrapper/proxyapplication/Util;->securename7:Ljava/lang/String;

    invoke-virtual {v5, v9}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    invoke-virtual {v5}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v5

    invoke-direct {v4, v5}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v2, v4}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 551
    :cond_1434
    invoke-virtual {v7, v11}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v2

    .line 552
    .end local v3    # "fileUnzip":Ljava/util/zip/ZipEntry;
    .local v2, "fileUnzip":Ljava/util/zip/ZipEntry;
    if-eqz v2, :cond_144c

    .line 553
    invoke-virtual {v2}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v3

    invoke-static {v12, v3, v4}, Lcom/wrapper/proxyapplication/Util;->isFileValid(Ljava/lang/String;J)Z

    move-result v3

    if-nez v3, :cond_144c

    .line 554
    new-instance v3, Ljava/io/File;

    invoke-direct {v3, v12}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    invoke-static {v7, v11, v3}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z

    .line 557
    :cond_144c
    if-eqz v27, :cond_150d

    .line 559
    :try_start_144e
    invoke-virtual/range {v27 .. v27}, Ljava/nio/channels/FileLock;->release()V
    :try_end_1451
    .catch Ljava/io/IOException; {:try_start_144e .. :try_end_1451} :catch_148f
    .catchall {:try_start_144e .. :try_end_1451} :catchall_148c

    .line 565
    if-eqz v26, :cond_150d

    .line 567
    :try_start_1453
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_1456
    .catch Ljava/io/IOException; {:try_start_1453 .. :try_end_1456} :catch_1468
    .catchall {:try_start_1453 .. :try_end_1456} :catchall_1465

    .line 573
    if-eqz v25, :cond_1463

    .line 575
    :try_start_1458
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_145b
    .catch Ljava/io/IOException; {:try_start_1458 .. :try_end_145b} :catch_145c

    .line 580
    goto :goto_1463

    .line 576
    :catch_145c
    move-exception v0

    move-object v1, v0

    .line 578
    .local v1, "e":Ljava/io/IOException;
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v1    # "e":Ljava/io/IOException;
    :cond_1463
    :goto_1463
    goto/16 :goto_150d

    .line 573
    :catchall_1465
    move-exception v0

    move-object v1, v0

    goto :goto_147e

    .line 568
    :catch_1468
    move-exception v0

    move-object v1, v0

    .line 570
    .restart local v1    # "e":Ljava/io/IOException;
    :try_start_146a
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V
    :try_end_146d
    .catchall {:try_start_146a .. :try_end_146d} :catchall_1465

    .line 571
    nop

    .line 573
    if-eqz v25, :cond_147c

    .line 575
    :try_start_1470
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_1473
    .catch Ljava/io/IOException; {:try_start_1470 .. :try_end_1473} :catch_1475

    .line 580
    const/4 v4, -0x1

    goto :goto_147d

    .line 576
    :catch_1475
    move-exception v0

    move-object v3, v0

    .line 578
    .local v3, "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_147c
    const/4 v4, -0x1

    .line 571
    :goto_147d
    return v4

    .line 573
    .end local v1    # "e":Ljava/io/IOException;
    :goto_147e
    if-eqz v25, :cond_148b

    .line 575
    :try_start_1480
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_1483
    .catch Ljava/io/IOException; {:try_start_1480 .. :try_end_1483} :catch_1484

    .line 580
    goto :goto_148b

    .line 576
    :catch_1484
    move-exception v0

    move-object v1, v0

    .line 578
    .restart local v1    # "e":Ljava/io/IOException;
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v1    # "e":Ljava/io/IOException;
    :cond_148b
    :goto_148b
    throw v1

    .line 565
    :catchall_148c
    move-exception v0

    move-object v1, v0

    goto :goto_14d3

    .line 560
    :catch_148f
    move-exception v0

    move-object v1, v0

    .line 562
    .restart local v1    # "e":Ljava/io/IOException;
    :try_start_1491
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V
    :try_end_1494
    .catchall {:try_start_1491 .. :try_end_1494} :catchall_148c

    .line 563
    nop

    .line 565
    if-eqz v26, :cond_14d1

    .line 567
    :try_start_1497
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_149a
    .catch Ljava/io/IOException; {:try_start_1497 .. :try_end_149a} :catch_14ad
    .catchall {:try_start_1497 .. :try_end_149a} :catchall_14aa

    .line 573
    if-eqz v25, :cond_14a8

    .line 575
    :try_start_149c
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_149f
    .catch Ljava/io/IOException; {:try_start_149c .. :try_end_149f} :catch_14a1

    .line 580
    const/4 v4, -0x1

    goto :goto_14d2

    .line 576
    :catch_14a1
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_14a8
    const/4 v4, -0x1

    goto :goto_14d2

    :catchall_14aa
    move-exception v0

    move-object v3, v0

    goto :goto_14c3

    .line 568
    :catch_14ad
    move-exception v0

    move-object v3, v0

    .line 570
    .restart local v3    # "e":Ljava/io/IOException;
    :try_start_14af
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V
    :try_end_14b2
    .catchall {:try_start_14af .. :try_end_14b2} :catchall_14aa

    .line 571
    nop

    .line 573
    if-eqz v25, :cond_14c1

    .line 575
    :try_start_14b5
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_14b8
    .catch Ljava/io/IOException; {:try_start_14b5 .. :try_end_14b8} :catch_14ba

    .line 580
    const/4 v5, -0x1

    goto :goto_14c2

    .line 576
    :catch_14ba
    move-exception v0

    move-object v4, v0

    .line 578
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v5, -0x1

    return v5

    .line 573
    .end local v4    # "e":Ljava/io/IOException;
    :cond_14c1
    const/4 v5, -0x1

    .line 571
    :goto_14c2
    return v5

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :goto_14c3
    if-eqz v25, :cond_14d0

    .line 575
    :try_start_14c5
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_14c8
    .catch Ljava/io/IOException; {:try_start_14c5 .. :try_end_14c8} :catch_14c9

    .line 580
    goto :goto_14d0

    .line 576
    :catch_14c9
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .end local v3    # "e":Ljava/io/IOException;
    :cond_14d0
    :goto_14d0
    throw v3

    .line 565
    :cond_14d1
    const/4 v4, -0x1

    .line 563
    :goto_14d2
    return v4

    .line 565
    .end local v1    # "e":Ljava/io/IOException;
    :goto_14d3
    if-eqz v26, :cond_150c

    .line 567
    :try_start_14d5
    invoke-virtual/range {v26 .. v26}, Ljava/nio/channels/FileChannel;->close()V
    :try_end_14d8
    .catch Ljava/io/IOException; {:try_start_14d5 .. :try_end_14d8} :catch_14e8
    .catchall {:try_start_14d5 .. :try_end_14d8} :catchall_14e5

    .line 573
    if-eqz v25, :cond_150c

    .line 575
    :try_start_14da
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_14dd
    .catch Ljava/io/IOException; {:try_start_14da .. :try_end_14dd} :catch_14de

    .line 580
    goto :goto_150c

    .line 576
    :catch_14de
    move-exception v0

    move-object v1, v0

    .line 578
    .restart local v1    # "e":Ljava/io/IOException;
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .line 573
    .end local v1    # "e":Ljava/io/IOException;
    :catchall_14e5
    move-exception v0

    move-object v1, v0

    goto :goto_14fe

    .line 568
    :catch_14e8
    move-exception v0

    move-object v1, v0

    .line 570
    .restart local v1    # "e":Ljava/io/IOException;
    :try_start_14ea
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V
    :try_end_14ed
    .catchall {:try_start_14ea .. :try_end_14ed} :catchall_14e5

    .line 571
    nop

    .line 573
    if-eqz v25, :cond_14fc

    .line 575
    :try_start_14f0
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_14f3
    .catch Ljava/io/IOException; {:try_start_14f0 .. :try_end_14f3} :catch_14f5

    .line 580
    const/4 v4, -0x1

    goto :goto_14fd

    .line 576
    :catch_14f5
    move-exception v0

    move-object v3, v0

    .line 578
    .restart local v3    # "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v4, -0x1

    return v4

    .line 573
    .end local v3    # "e":Ljava/io/IOException;
    :cond_14fc
    const/4 v4, -0x1

    .line 571
    :goto_14fd
    return v4

    .line 573
    .end local v1    # "e":Ljava/io/IOException;
    :goto_14fe
    if-eqz v25, :cond_150b

    .line 575
    :try_start_1500
    invoke-virtual/range {v25 .. v25}, Ljava/io/RandomAccessFile;->close()V
    :try_end_1503
    .catch Ljava/io/IOException; {:try_start_1500 .. :try_end_1503} :catch_1504

    .line 580
    goto :goto_150b

    .line 576
    :catch_1504
    move-exception v0

    move-object v1, v0

    .line 578
    .restart local v1    # "e":Ljava/io/IOException;
    invoke-virtual {v1}, Ljava/io/IOException;->printStackTrace()V

    .line 579
    const/4 v3, -0x1

    return v3

    .end local v1    # "e":Ljava/io/IOException;
    :cond_150b
    :goto_150b
    throw v1

    :cond_150c
    :goto_150c
    throw v1

    .line 586
    .end local v2    # "fileUnzip":Ljava/util/zip/ZipEntry;
    :cond_150d
    :goto_150d
    throw v1
.end method

.method public static SafeUnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z
    .registers 5
    .param p0, "zf"    # Ljava/util/zip/ZipFile;
    .param p1, "filepathinzip"    # Ljava/lang/String;
    .param p2, "fileinfiledir"    # Ljava/io/File;

    .line 701
    const-wide/16 v0, 0x0

    invoke-static {p0, p1, p2, v0, v1}, Lcom/wrapper/proxyapplication/Util;->SafeUnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;J)Z

    move-result v0

    return v0
.end method

.method public static SafeUnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;J)Z
    .registers 15
    .param p0, "zf"    # Ljava/util/zip/ZipFile;
    .param p1, "filepathinzip"    # Ljava/lang/String;
    .param p2, "fileinfiledir"    # Ljava/io/File;
    .param p3, "crc"    # J

    .line 705
    const/4 v0, 0x0

    .line 706
    .local v0, "Output_fos":Ljava/io/BufferedOutputStream;
    const/4 v1, 0x0

    .line 707
    .local v1, "bufbr":Ljava/io/BufferedInputStream;
    const/4 v2, 0x0

    .line 708
    .local v2, "ze":Ljava/util/zip/ZipEntry;
    const/4 v3, 0x0

    .line 709
    .local v3, "buf":[B
    const/4 v4, 0x0

    .line 712
    .local v4, "ifoverwrite":Z
    const/4 v5, 0x0

    :try_start_6
    invoke-virtual {p0, p1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v6
    :try_end_a
    .catch Ljava/lang/Exception; {:try_start_6 .. :try_end_a} :catch_c8
    .catchall {:try_start_6 .. :try_end_a} :catchall_c6

    move-object v2, v6

    .line 714
    if-nez v2, :cond_3e

    .line 716
    nop

    .line 744
    if-eqz v0, :cond_3d

    .line 746
    :try_start_10
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_13
    .catch Ljava/io/IOException; {:try_start_10 .. :try_end_13} :catch_20
    .catchall {:try_start_10 .. :try_end_13} :catchall_1e

    .line 752
    if-eqz v1, :cond_3d

    .line 754
    :try_start_15
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_18
    .catch Ljava/io/IOException; {:try_start_15 .. :try_end_18} :catch_19

    .line 759
    goto :goto_3d

    .line 755
    :catch_19
    move-exception v6

    .line 757
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :catchall_1e
    move-exception v6

    goto :goto_31

    .line 747
    :catch_20
    move-exception v6

    .line 749
    .restart local v6    # "e":Ljava/io/IOException;
    :try_start_21
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V
    :try_end_24
    .catchall {:try_start_21 .. :try_end_24} :catchall_1e

    .line 750
    nop

    .line 752
    if-eqz v1, :cond_30

    .line 754
    :try_start_27
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_2a
    .catch Ljava/io/IOException; {:try_start_27 .. :try_end_2a} :catch_2b

    .line 759
    goto :goto_30

    .line 755
    :catch_2b
    move-exception v7

    .line 757
    .local v7, "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 750
    .end local v7    # "e":Ljava/io/IOException;
    :cond_30
    :goto_30
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :goto_31
    if-eqz v1, :cond_3c

    .line 754
    :try_start_33
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_36
    .catch Ljava/io/IOException; {:try_start_33 .. :try_end_36} :catch_37

    .line 759
    goto :goto_3c

    .line 755
    :catch_37
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .end local v6    # "e":Ljava/io/IOException;
    :cond_3c
    :goto_3c
    throw v6

    .line 716
    :cond_3d
    :goto_3d
    return v5

    .line 718
    :cond_3e
    const-wide/16 v6, 0x0

    const/4 v8, 0x1

    cmp-long v9, p3, v6

    if-eqz v9, :cond_7e

    :try_start_45
    invoke-virtual {v2}, Ljava/util/zip/ZipEntry;->getCrc()J

    move-result-wide v6
    :try_end_49
    .catch Ljava/lang/Exception; {:try_start_45 .. :try_end_49} :catch_c8
    .catchall {:try_start_45 .. :try_end_49} :catchall_c6

    cmp-long v9, v6, p3

    if-nez v9, :cond_7e

    .line 720
    nop

    .line 744
    if-eqz v0, :cond_7d

    .line 746
    :try_start_50
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_53
    .catch Ljava/io/IOException; {:try_start_50 .. :try_end_53} :catch_60
    .catchall {:try_start_50 .. :try_end_53} :catchall_5e

    .line 752
    if-eqz v1, :cond_7d

    .line 754
    :try_start_55
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_58
    .catch Ljava/io/IOException; {:try_start_55 .. :try_end_58} :catch_59

    .line 759
    goto :goto_7d

    .line 755
    :catch_59
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :catchall_5e
    move-exception v6

    goto :goto_71

    .line 747
    :catch_60
    move-exception v6

    .line 749
    .restart local v6    # "e":Ljava/io/IOException;
    :try_start_61
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V
    :try_end_64
    .catchall {:try_start_61 .. :try_end_64} :catchall_5e

    .line 750
    nop

    .line 752
    if-eqz v1, :cond_70

    .line 754
    :try_start_67
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_6a
    .catch Ljava/io/IOException; {:try_start_67 .. :try_end_6a} :catch_6b

    .line 759
    goto :goto_70

    .line 755
    :catch_6b
    move-exception v7

    .line 757
    .restart local v7    # "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 750
    .end local v7    # "e":Ljava/io/IOException;
    :cond_70
    :goto_70
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :goto_71
    if-eqz v1, :cond_7c

    .line 754
    :try_start_73
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_76
    .catch Ljava/io/IOException; {:try_start_73 .. :try_end_76} :catch_77

    .line 759
    goto :goto_7c

    .line 755
    :catch_77
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .end local v6    # "e":Ljava/io/IOException;
    :cond_7c
    :goto_7c
    throw v6

    .line 720
    :cond_7d
    :goto_7d
    return v8

    .line 722
    :cond_7e
    const/4 v4, 0x1

    .line 723
    :try_start_7f
    invoke-static {p0, v2}, Lcom/wrapper/proxyapplication/Util;->UnzipFile(Ljava/util/zip/ZipFile;Ljava/util/zip/ZipEntry;)[B

    move-result-object v6

    move-object v3, v6

    .line 732
    if-eqz v4, :cond_95

    .line 733
    new-instance v6, Ljava/io/BufferedOutputStream;

    new-instance v7, Ljava/io/FileOutputStream;

    invoke-direct {v7, p2}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    invoke-direct {v6, v7}, Ljava/io/BufferedOutputStream;-><init>(Ljava/io/OutputStream;)V

    move-object v0, v6

    .line 734
    array-length v6, v3

    invoke-virtual {v0, v3, v5, v6}, Ljava/io/BufferedOutputStream;->write([BII)V
    :try_end_95
    .catch Ljava/lang/Exception; {:try_start_7f .. :try_end_95} :catch_c8
    .catchall {:try_start_7f .. :try_end_95} :catchall_c6

    .line 737
    :cond_95
    nop

    .line 744
    if-eqz v0, :cond_c5

    .line 746
    :try_start_98
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_9b
    .catch Ljava/io/IOException; {:try_start_98 .. :try_end_9b} :catch_a8
    .catchall {:try_start_98 .. :try_end_9b} :catchall_a6

    .line 752
    if-eqz v1, :cond_c5

    .line 754
    :try_start_9d
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_a0
    .catch Ljava/io/IOException; {:try_start_9d .. :try_end_a0} :catch_a1

    .line 759
    goto :goto_c5

    .line 755
    :catch_a1
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :catchall_a6
    move-exception v6

    goto :goto_b9

    .line 747
    :catch_a8
    move-exception v6

    .line 749
    .restart local v6    # "e":Ljava/io/IOException;
    :try_start_a9
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V
    :try_end_ac
    .catchall {:try_start_a9 .. :try_end_ac} :catchall_a6

    .line 750
    nop

    .line 752
    if-eqz v1, :cond_b8

    .line 754
    :try_start_af
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_b2
    .catch Ljava/io/IOException; {:try_start_af .. :try_end_b2} :catch_b3

    .line 759
    goto :goto_b8

    .line 755
    :catch_b3
    move-exception v7

    .line 757
    .restart local v7    # "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 750
    .end local v7    # "e":Ljava/io/IOException;
    :cond_b8
    :goto_b8
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :goto_b9
    if-eqz v1, :cond_c4

    .line 754
    :try_start_bb
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_be
    .catch Ljava/io/IOException; {:try_start_bb .. :try_end_be} :catch_bf

    .line 759
    goto :goto_c4

    .line 755
    :catch_bf
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .end local v6    # "e":Ljava/io/IOException;
    :cond_c4
    :goto_c4
    throw v6

    .line 737
    :cond_c5
    :goto_c5
    return v8

    .line 744
    :catchall_c6
    move-exception v6

    goto :goto_fd

    .line 739
    :catch_c8
    move-exception v6

    .line 741
    .local v6, "e":Ljava/lang/Exception;
    :try_start_c9
    invoke-virtual {v6}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_cc
    .catchall {:try_start_c9 .. :try_end_cc} :catchall_c6

    .line 742
    nop

    .line 744
    if-eqz v0, :cond_fc

    .line 746
    :try_start_cf
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_d2
    .catch Ljava/io/IOException; {:try_start_cf .. :try_end_d2} :catch_df
    .catchall {:try_start_cf .. :try_end_d2} :catchall_dd

    .line 752
    if-eqz v1, :cond_fc

    .line 754
    :try_start_d4
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_d7
    .catch Ljava/io/IOException; {:try_start_d4 .. :try_end_d7} :catch_d8

    .line 759
    goto :goto_fc

    .line 755
    :catch_d8
    move-exception v7

    .line 757
    .restart local v7    # "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 752
    .end local v7    # "e":Ljava/io/IOException;
    :catchall_dd
    move-exception v7

    goto :goto_f0

    .line 747
    :catch_df
    move-exception v7

    .line 749
    .restart local v7    # "e":Ljava/io/IOException;
    :try_start_e0
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V
    :try_end_e3
    .catchall {:try_start_e0 .. :try_end_e3} :catchall_dd

    .line 750
    nop

    .line 752
    if-eqz v1, :cond_ef

    .line 754
    :try_start_e6
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_e9
    .catch Ljava/io/IOException; {:try_start_e6 .. :try_end_e9} :catch_ea

    .line 759
    goto :goto_ef

    .line 755
    :catch_ea
    move-exception v8

    .line 757
    .local v8, "e":Ljava/io/IOException;
    invoke-virtual {v8}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 750
    .end local v8    # "e":Ljava/io/IOException;
    :cond_ef
    :goto_ef
    return v5

    .line 752
    .end local v7    # "e":Ljava/io/IOException;
    :goto_f0
    if-eqz v1, :cond_fb

    .line 754
    :try_start_f2
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_f5
    .catch Ljava/io/IOException; {:try_start_f2 .. :try_end_f5} :catch_f6

    .line 759
    goto :goto_fb

    .line 755
    :catch_f6
    move-exception v7

    .line 757
    .restart local v7    # "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .end local v7    # "e":Ljava/io/IOException;
    :cond_fb
    :goto_fb
    throw v7

    .line 742
    :cond_fc
    :goto_fc
    return v5

    .line 744
    .end local v6    # "e":Ljava/lang/Exception;
    :goto_fd
    if-eqz v0, :cond_12c

    .line 746
    :try_start_ff
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_102
    .catch Ljava/io/IOException; {:try_start_ff .. :try_end_102} :catch_10f
    .catchall {:try_start_ff .. :try_end_102} :catchall_10d

    .line 752
    if-eqz v1, :cond_12c

    .line 754
    :try_start_104
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_107
    .catch Ljava/io/IOException; {:try_start_104 .. :try_end_107} :catch_108

    .line 759
    goto :goto_12c

    .line 755
    :catch_108
    move-exception v6

    .line 757
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :catchall_10d
    move-exception v6

    goto :goto_120

    .line 747
    :catch_10f
    move-exception v6

    .line 749
    .restart local v6    # "e":Ljava/io/IOException;
    :try_start_110
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V
    :try_end_113
    .catchall {:try_start_110 .. :try_end_113} :catchall_10d

    .line 750
    nop

    .line 752
    if-eqz v1, :cond_11f

    .line 754
    :try_start_116
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_119
    .catch Ljava/io/IOException; {:try_start_116 .. :try_end_119} :catch_11a

    .line 759
    goto :goto_11f

    .line 755
    :catch_11a
    move-exception v7

    .line 757
    .restart local v7    # "e":Ljava/io/IOException;
    invoke-virtual {v7}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .line 750
    .end local v7    # "e":Ljava/io/IOException;
    :cond_11f
    :goto_11f
    return v5

    .line 752
    .end local v6    # "e":Ljava/io/IOException;
    :goto_120
    if-eqz v1, :cond_12b

    .line 754
    :try_start_122
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_125
    .catch Ljava/io/IOException; {:try_start_122 .. :try_end_125} :catch_126

    .line 759
    goto :goto_12b

    .line 755
    :catch_126
    move-exception v6

    .line 757
    .restart local v6    # "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 758
    return v5

    .end local v6    # "e":Ljava/io/IOException;
    :cond_12b
    :goto_12b
    throw v6

    :cond_12c
    :goto_12c
    throw v6
.end method

.method public static UnzipFile(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z
    .registers 10
    .param p0, "zf"    # Ljava/util/zip/ZipFile;
    .param p1, "filepathinzip"    # Ljava/lang/String;
    .param p2, "fileinfiledir"    # Ljava/io/File;

    .line 190
    const/4 v0, 0x0

    .line 192
    .local v0, "Output_fos":Ljava/io/BufferedOutputStream;
    const/4 v1, 0x0

    .line 194
    .local v1, "bufbr":Ljava/io/BufferedInputStream;
    const/4 v2, 0x0

    .line 198
    .local v2, "ze":Ljava/util/zip/ZipEntry;
    const/4 v3, 0x0

    :try_start_4
    invoke-virtual {p0, p1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v4
    :try_end_8
    .catch Ljava/lang/Exception; {:try_start_4 .. :try_end_8} :catch_8f
    .catchall {:try_start_4 .. :try_end_8} :catchall_8d

    move-object v2, v4

    .line 200
    if-nez v2, :cond_3c

    .line 202
    nop

    .line 228
    if-eqz v0, :cond_3b

    .line 231
    :try_start_e
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_11
    .catch Ljava/io/IOException; {:try_start_e .. :try_end_11} :catch_1e
    .catchall {:try_start_e .. :try_end_11} :catchall_1c

    .line 237
    if-eqz v1, :cond_3b

    .line 239
    :try_start_13
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_16
    .catch Ljava/io/IOException; {:try_start_13 .. :try_end_16} :catch_17

    .line 244
    goto :goto_3b

    .line 240
    :catch_17
    move-exception v4

    .line 242
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 237
    .end local v4    # "e":Ljava/io/IOException;
    :catchall_1c
    move-exception v4

    goto :goto_2f

    .line 232
    :catch_1e
    move-exception v4

    .line 234
    .restart local v4    # "e":Ljava/io/IOException;
    :try_start_1f
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V
    :try_end_22
    .catchall {:try_start_1f .. :try_end_22} :catchall_1c

    .line 235
    nop

    .line 237
    if-eqz v1, :cond_2e

    .line 239
    :try_start_25
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_28
    .catch Ljava/io/IOException; {:try_start_25 .. :try_end_28} :catch_29

    .line 244
    goto :goto_2e

    .line 240
    :catch_29
    move-exception v5

    .line 242
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 235
    .end local v5    # "e":Ljava/io/IOException;
    :cond_2e
    :goto_2e
    return v3

    .line 237
    .end local v4    # "e":Ljava/io/IOException;
    :goto_2f
    if-eqz v1, :cond_3a

    .line 239
    :try_start_31
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_34
    .catch Ljava/io/IOException; {:try_start_31 .. :try_end_34} :catch_35

    .line 244
    goto :goto_3a

    .line 240
    :catch_35
    move-exception v4

    .line 242
    .restart local v4    # "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .end local v4    # "e":Ljava/io/IOException;
    :cond_3a
    :goto_3a
    throw v4

    .line 202
    :cond_3b
    :goto_3b
    return v3

    .line 205
    :cond_3c
    :try_start_3c
    new-instance v4, Ljava/io/BufferedOutputStream;

    new-instance v5, Ljava/io/FileOutputStream;

    invoke-direct {v5, p2}, Ljava/io/FileOutputStream;-><init>(Ljava/io/File;)V

    invoke-direct {v4, v5}, Ljava/io/BufferedOutputStream;-><init>(Ljava/io/OutputStream;)V

    move-object v0, v4

    .line 206
    const/high16 v4, 0x10000

    new-array v4, v4, [B

    .line 208
    .local v4, "buf":[B
    new-instance v5, Ljava/io/BufferedInputStream;

    invoke-virtual {p0, v2}, Ljava/util/zip/ZipFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v6

    invoke-direct {v5, v6}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    move-object v1, v5

    .line 211
    :goto_55
    invoke-virtual {v1, v4}, Ljava/io/BufferedInputStream;->read([B)I

    move-result v5
    :try_end_59
    .catch Ljava/lang/Exception; {:try_start_3c .. :try_end_59} :catch_8f
    .catchall {:try_start_3c .. :try_end_59} :catchall_8d

    .line 212
    .local v5, "readlen":I
    if-gez v5, :cond_89

    .line 213
    nop

    .line 228
    .end local v4    # "buf":[B
    .end local v5    # "readlen":I
    nop

    .line 231
    :try_start_5d
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_60
    .catch Ljava/io/IOException; {:try_start_5d .. :try_end_60} :catch_6f
    .catchall {:try_start_5d .. :try_end_60} :catchall_6d

    .line 237
    nop

    .line 239
    :try_start_61
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_64
    .catch Ljava/io/IOException; {:try_start_61 .. :try_end_64} :catch_68

    .line 244
    nop

    .line 243
    nop

    .line 249
    const/4 v3, 0x1

    return v3

    .line 240
    :catch_68
    move-exception v4

    .line 242
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 237
    .end local v4    # "e":Ljava/io/IOException;
    :catchall_6d
    move-exception v4

    goto :goto_7f

    .line 232
    :catch_6f
    move-exception v4

    .line 234
    .restart local v4    # "e":Ljava/io/IOException;
    :try_start_70
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V
    :try_end_73
    .catchall {:try_start_70 .. :try_end_73} :catchall_6d

    .line 235
    nop

    .line 237
    nop

    .line 239
    :try_start_75
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_78
    .catch Ljava/io/IOException; {:try_start_75 .. :try_end_78} :catch_7a

    .line 244
    nop

    .line 235
    return v3

    .line 240
    :catch_7a
    move-exception v5

    .line 242
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 239
    .end local v4    # "e":Ljava/io/IOException;
    .end local v5    # "e":Ljava/io/IOException;
    :goto_7f
    :try_start_7f
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_82
    .catch Ljava/io/IOException; {:try_start_7f .. :try_end_82} :catch_84

    .line 244
    nop

    .line 243
    throw v4

    .line 240
    :catch_84
    move-exception v4

    .line 242
    .restart local v4    # "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 216
    .local v4, "buf":[B
    .local v5, "readlen":I
    :cond_89
    :try_start_89
    invoke-virtual {v0, v4, v3, v5}, Ljava/io/BufferedOutputStream;->write([BII)V
    :try_end_8c
    .catch Ljava/lang/Exception; {:try_start_89 .. :try_end_8c} :catch_8f
    .catchall {:try_start_89 .. :try_end_8c} :catchall_8d

    .line 218
    .end local v5    # "readlen":I
    goto :goto_55

    .line 228
    .end local v4    # "buf":[B
    :catchall_8d
    move-exception v4

    goto :goto_c4

    .line 220
    :catch_8f
    move-exception v4

    .line 222
    .local v4, "e":Ljava/lang/Exception;
    :try_start_90
    invoke-virtual {v4}, Ljava/lang/Exception;->printStackTrace()V
    :try_end_93
    .catchall {:try_start_90 .. :try_end_93} :catchall_8d

    .line 223
    nop

    .line 228
    if-eqz v0, :cond_c3

    .line 231
    :try_start_96
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_99
    .catch Ljava/io/IOException; {:try_start_96 .. :try_end_99} :catch_a6
    .catchall {:try_start_96 .. :try_end_99} :catchall_a4

    .line 237
    if-eqz v1, :cond_c3

    .line 239
    :try_start_9b
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_9e
    .catch Ljava/io/IOException; {:try_start_9b .. :try_end_9e} :catch_9f

    .line 244
    goto :goto_c3

    .line 240
    :catch_9f
    move-exception v5

    .line 242
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 237
    .end local v5    # "e":Ljava/io/IOException;
    :catchall_a4
    move-exception v5

    goto :goto_b7

    .line 232
    :catch_a6
    move-exception v5

    .line 234
    .restart local v5    # "e":Ljava/io/IOException;
    :try_start_a7
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V
    :try_end_aa
    .catchall {:try_start_a7 .. :try_end_aa} :catchall_a4

    .line 235
    nop

    .line 237
    if-eqz v1, :cond_b6

    .line 239
    :try_start_ad
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_b0
    .catch Ljava/io/IOException; {:try_start_ad .. :try_end_b0} :catch_b1

    .line 244
    goto :goto_b6

    .line 240
    :catch_b1
    move-exception v6

    .line 242
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 235
    .end local v6    # "e":Ljava/io/IOException;
    :cond_b6
    :goto_b6
    return v3

    .line 237
    .end local v5    # "e":Ljava/io/IOException;
    :goto_b7
    if-eqz v1, :cond_c2

    .line 239
    :try_start_b9
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_bc
    .catch Ljava/io/IOException; {:try_start_b9 .. :try_end_bc} :catch_bd

    .line 244
    goto :goto_c2

    .line 240
    :catch_bd
    move-exception v5

    .line 242
    .restart local v5    # "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .end local v5    # "e":Ljava/io/IOException;
    :cond_c2
    :goto_c2
    throw v5

    .line 223
    :cond_c3
    :goto_c3
    return v3

    .line 228
    .end local v4    # "e":Ljava/lang/Exception;
    :goto_c4
    if-eqz v0, :cond_f3

    .line 231
    :try_start_c6
    invoke-virtual {v0}, Ljava/io/BufferedOutputStream;->close()V
    :try_end_c9
    .catch Ljava/io/IOException; {:try_start_c6 .. :try_end_c9} :catch_d6
    .catchall {:try_start_c6 .. :try_end_c9} :catchall_d4

    .line 237
    if-eqz v1, :cond_f3

    .line 239
    :try_start_cb
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_ce
    .catch Ljava/io/IOException; {:try_start_cb .. :try_end_ce} :catch_cf

    .line 244
    goto :goto_f3

    .line 240
    :catch_cf
    move-exception v4

    .line 242
    .local v4, "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 237
    .end local v4    # "e":Ljava/io/IOException;
    :catchall_d4
    move-exception v4

    goto :goto_e7

    .line 232
    :catch_d6
    move-exception v4

    .line 234
    .restart local v4    # "e":Ljava/io/IOException;
    :try_start_d7
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V
    :try_end_da
    .catchall {:try_start_d7 .. :try_end_da} :catchall_d4

    .line 235
    nop

    .line 237
    if-eqz v1, :cond_e6

    .line 239
    :try_start_dd
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_e0
    .catch Ljava/io/IOException; {:try_start_dd .. :try_end_e0} :catch_e1

    .line 244
    goto :goto_e6

    .line 240
    :catch_e1
    move-exception v5

    .line 242
    .restart local v5    # "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .line 235
    .end local v5    # "e":Ljava/io/IOException;
    :cond_e6
    :goto_e6
    return v3

    .line 237
    .end local v4    # "e":Ljava/io/IOException;
    :goto_e7
    if-eqz v1, :cond_f2

    .line 239
    :try_start_e9
    invoke-virtual {v1}, Ljava/io/BufferedInputStream;->close()V
    :try_end_ec
    .catch Ljava/io/IOException; {:try_start_e9 .. :try_end_ec} :catch_ed

    .line 244
    goto :goto_f2

    .line 240
    :catch_ed
    move-exception v4

    .line 242
    .restart local v4    # "e":Ljava/io/IOException;
    invoke-virtual {v4}, Ljava/io/IOException;->printStackTrace()V

    .line 243
    return v3

    .end local v4    # "e":Ljava/io/IOException;
    :cond_f2
    :goto_f2
    throw v4

    :cond_f3
    :goto_f3
    throw v4
.end method

.method public static UnzipFile(Ljava/util/zip/ZipFile;Ljava/util/zip/ZipEntry;)[B
    .registers 11
    .param p0, "zf"    # Ljava/util/zip/ZipFile;
    .param p1, "ze"    # Ljava/util/zip/ZipEntry;
    .annotation system Ldalvik/annotation/Throws;
        value = {
            Ljava/io/IOException;
        }
    .end annotation

    .line 770
    invoke-virtual {p1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v0

    long-to-int v1, v0

    new-array v0, v1, [B

    .line 771
    .local v0, "buf":[B
    new-instance v1, Ljava/io/BufferedInputStream;

    invoke-virtual {p0, p1}, Ljava/util/zip/ZipFile;->getInputStream(Ljava/util/zip/ZipEntry;)Ljava/io/InputStream;

    move-result-object v2

    invoke-direct {v1, v2}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    .line 773
    .local v1, "bufbr":Ljava/io/BufferedInputStream;
    const/4 v2, 0x0

    .line 775
    .local v2, "totallen":I
    :goto_11
    invoke-virtual {p1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v3

    long-to-int v4, v3

    sub-int/2addr v4, v2

    invoke-virtual {v1, v0, v2, v4}, Ljava/io/BufferedInputStream;->read([BII)I

    move-result v3

    .line 776
    .local v3, "readlen":I
    if-gez v3, :cond_1e

    .line 777
    goto :goto_29

    .line 779
    :cond_1e
    add-int/2addr v2, v3

    .line 780
    int-to-long v4, v2

    invoke-virtual {p1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v6

    cmp-long v8, v4, v6

    if-nez v8, :cond_39

    .line 781
    nop

    .line 786
    .end local v3    # "readlen":I
    :goto_29
    invoke-virtual {p1}, Ljava/util/zip/ZipEntry;->getSize()J

    move-result-wide v3

    long-to-int v4, v3

    if-ne v2, v4, :cond_31

    .line 790
    return-object v0

    .line 788
    :cond_31
    new-instance v3, Ljava/io/IOException;

    const-string v4, "incorrect zip file size"

    invoke-direct {v3, v4}, Ljava/io/IOException;-><init>(Ljava/lang/String;)V

    throw v3

    .line 785
    :cond_39
    goto :goto_11
.end method

.method private static checkCopiedFileCrc(Ljava/util/zip/ZipFile;Ljava/lang/String;Ljava/io/File;)Z
    .registers 10
    .param p0, "zf"    # Ljava/util/zip/ZipFile;
    .param p1, "filepathinzip"    # Ljava/lang/String;
    .param p2, "file"    # Ljava/io/File;

    .line 837
    invoke-static {p2}, Lcom/wrapper/proxyapplication/Util;->getFileCRC32(Ljava/io/File;)J

    move-result-wide v0

    .line 839
    .local v0, "crc":J
    const/4 v2, 0x0

    const-wide/16 v3, -0x1

    cmp-long v5, v0, v3

    if-nez v5, :cond_c

    .line 840
    return v2

    .line 841
    :cond_c
    const/4 v3, 0x0

    .line 844
    .local v3, "ze":Ljava/util/zip/ZipEntry;
    :try_start_d
    invoke-virtual {p0, p1}, Ljava/util/zip/ZipFile;->getEntry(Ljava/lang/String;)Ljava/util/zip/ZipEntry;

    move-result-object v4

    move-object v3, v4

    .line 846
    if-nez v3, :cond_15

    .line 848
    return v2

    .line 850
    :cond_15
    const-wide/16 v4, 0x0

    cmp-long v6, v0, v4

    if-eqz v6, :cond_25

    invoke-virtual {v3}, Ljava/util/zip/ZipEntry;->getCrc()J

    move-result-wide v4
    :try_end_1f
    .catch Ljava/lang/Exception; {:try_start_d .. :try_end_1f} :catch_27

    cmp-long v6, v4, v0

    if-nez v6, :cond_25

    .line 853
    const/4 v2, 0x1

    return v2

    .line 858
    :cond_25
    nop

    .line 859
    return v2

    .line 856
    :catch_27
    move-exception v4

    .line 857
    .local v4, "e":Ljava/lang/Exception;
    return v2
.end method

.method public static deleteDir(Ljava/io/File;)Z
    .registers 6
    .param p0, "file"    # Ljava/io/File;

    .line 317
    const/4 v0, 0x1

    .line 318
    .local v0, "result":Z
    invoke-virtual {p0}, Ljava/io/File;->isDirectory()Z

    move-result v1

    if-eqz v1, :cond_21

    .line 319
    invoke-virtual {p0}, Ljava/io/File;->list()[Ljava/lang/String;

    move-result-object v1

    .line 321
    .local v1, "children":[Ljava/lang/String;
    const/4 v2, 0x0

    .local v2, "i":I
    :goto_c
    array-length v3, v1

    if-ge v2, v3, :cond_21

    .line 322
    new-instance v3, Ljava/io/File;

    aget-object v4, v1, v2

    invoke-direct {v3, p0, v4}, Ljava/io/File;-><init>(Ljava/io/File;Ljava/lang/String;)V

    invoke-static {v3}, Lcom/wrapper/proxyapplication/Util;->deleteDir(Ljava/io/File;)Z

    move-result v3

    .line 323
    .local v3, "success":Z
    if-nez v3, :cond_1e

    .line 324
    const/4 v4, 0x0

    return v4

    .line 321
    .end local v3    # "success":Z
    :cond_1e
    add-int/lit8 v2, v2, 0x1

    goto :goto_c

    .line 329
    .end local v1    # "children":[Ljava/lang/String;
    .end local v2    # "i":I
    :cond_21
    invoke-virtual {p0}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_2b

    .line 330
    invoke-virtual {p0}, Ljava/io/File;->delete()Z

    move-result v0

    .line 333
    :cond_2b
    return v0
.end method

.method public static getCRC32(Ljava/io/File;)J
    .registers 9
    .param p0, "fileUri"    # Ljava/io/File;

    .line 652
    new-instance v0, Ljava/util/zip/CRC32;

    invoke-direct {v0}, Ljava/util/zip/CRC32;-><init>()V

    .line 654
    .local v0, "crc32":Ljava/util/zip/CRC32;
    const/4 v1, 0x0

    .line 655
    .local v1, "bufbr":Ljava/io/BufferedInputStream;
    const/4 v2, 0x0

    .line 656
    .local v2, "checkedinputstream":Ljava/util/zip/CheckedInputStream;
    const-wide/16 v3, 0x0

    .line 659
    .local v3, "crc":J
    :try_start_9
    new-instance v5, Ljava/io/BufferedInputStream;

    new-instance v6, Ljava/io/FileInputStream;

    invoke-direct {v6, p0}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V

    invoke-direct {v5, v6}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    move-object v1, v5

    .line 660
    new-instance v5, Ljava/util/zip/CheckedInputStream;

    invoke-direct {v5, v1, v0}, Ljava/util/zip/CheckedInputStream;-><init>(Ljava/io/InputStream;Ljava/util/zip/Checksum;)V

    move-object v2, v5

    .line 661
    const/high16 v5, 0x10000

    new-array v5, v5, [B

    .line 662
    .local v5, "buf":[B
    :goto_1e
    invoke-virtual {v2, v5}, Ljava/util/zip/CheckedInputStream;->read([B)I

    move-result v6

    if-ltz v6, :cond_25

    goto :goto_1e

    .line 665
    :cond_25
    invoke-virtual {v2}, Ljava/util/zip/CheckedInputStream;->getChecksum()Ljava/util/zip/Checksum;

    move-result-object v6

    invoke-interface {v6}, Ljava/util/zip/Checksum;->getValue()J

    move-result-wide v6

    move-wide v3, v6

    .line 666
    invoke-virtual {v2}, Ljava/util/zip/CheckedInputStream;->close()V
    :try_end_31
    .catch Ljava/io/FileNotFoundException; {:try_start_9 .. :try_end_31} :catch_39
    .catch Ljava/io/IOException; {:try_start_9 .. :try_end_31} :catch_34
    .catchall {:try_start_9 .. :try_end_31} :catchall_32

    .end local v5    # "buf":[B
    goto :goto_3d

    .line 671
    :catchall_32
    move-exception v5

    goto :goto_3f

    .line 669
    :catch_34
    move-exception v5

    .line 670
    .local v5, "e":Ljava/io/IOException;
    :try_start_35
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .end local v5    # "e":Ljava/io/IOException;
    goto :goto_3d

    .line 667
    :catch_39
    move-exception v5

    .line 668
    .local v5, "e":Ljava/io/FileNotFoundException;
    invoke-virtual {v5}, Ljava/io/FileNotFoundException;->printStackTrace()V
    :try_end_3d
    .catchall {:try_start_35 .. :try_end_3d} :catchall_32

    .line 686
    .end local v5    # "e":Ljava/io/FileNotFoundException;
    :goto_3d
    nop

    .line 687
    return-wide v3

    .line 671
    :goto_3f
    throw v5
.end method

.method private static getFileCRC32(Ljava/io/File;)J
    .registers 9
    .param p0, "file"    # Ljava/io/File;

    .line 795
    const-wide/16 v0, -0x1

    .line 796
    .local v0, "result":J
    invoke-virtual {p0}, Ljava/io/File;->length()J

    move-result-wide v2

    long-to-int v3, v2

    new-array v2, v3, [B

    .line 797
    .local v2, "filebuf":[B
    const/4 v3, 0x0

    .line 798
    .local v3, "filebr":Ljava/io/BufferedInputStream;
    new-instance v4, Ljava/util/zip/CRC32;

    invoke-direct {v4}, Ljava/util/zip/CRC32;-><init>()V

    .line 800
    .local v4, "crc32":Ljava/util/zip/CRC32;
    :try_start_f
    new-instance v5, Ljava/io/BufferedInputStream;

    new-instance v6, Ljava/io/FileInputStream;

    invoke-direct {v6, p0}, Ljava/io/FileInputStream;-><init>(Ljava/io/File;)V

    invoke-direct {v5, v6}, Ljava/io/BufferedInputStream;-><init>(Ljava/io/InputStream;)V

    move-object v3, v5

    .line 801
    const/4 v5, 0x0

    .line 803
    .local v5, "totallen":I
    :goto_1b
    invoke-virtual {v3, v2}, Ljava/io/BufferedInputStream;->read([B)I

    move-result v6

    .line 804
    .local v6, "readlen":I
    if-gez v6, :cond_31

    .line 805
    nop

    .line 814
    .end local v6    # "readlen":I
    invoke-virtual {v4}, Ljava/util/zip/CRC32;->getValue()J

    move-result-wide v6
    :try_end_26
    .catch Ljava/io/FileNotFoundException; {:try_start_f .. :try_end_26} :catch_42
    .catch Ljava/io/IOException; {:try_start_f .. :try_end_26} :catch_38
    .catchall {:try_start_f .. :try_end_26} :catchall_36

    move-wide v0, v6

    .line 822
    .end local v5    # "totallen":I
    nop

    .line 824
    :try_start_28
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_2b
    .catch Ljava/io/IOException; {:try_start_28 .. :try_end_2b} :catch_2c

    .line 828
    :goto_2b
    goto :goto_4c

    .line 825
    :catch_2c
    move-exception v5

    .line 827
    .local v5, "e":Ljava/io/IOException;
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V

    .end local v5    # "e":Ljava/io/IOException;
    goto :goto_2b

    .line 807
    .local v5, "totallen":I
    .restart local v6    # "readlen":I
    :cond_31
    :try_start_31
    invoke-virtual {v4, v2}, Ljava/util/zip/CRC32;->update([B)V
    :try_end_34
    .catch Ljava/io/FileNotFoundException; {:try_start_31 .. :try_end_34} :catch_42
    .catch Ljava/io/IOException; {:try_start_31 .. :try_end_34} :catch_38
    .catchall {:try_start_31 .. :try_end_34} :catchall_36

    .line 808
    add-int/2addr v5, v6

    .line 812
    .end local v6    # "readlen":I
    goto :goto_1b

    .line 822
    .end local v5    # "totallen":I
    :catchall_36
    move-exception v5

    goto :goto_4d

    .line 818
    :catch_38
    move-exception v5

    .line 820
    .local v5, "e":Ljava/io/IOException;
    :try_start_39
    invoke-virtual {v5}, Ljava/io/IOException;->printStackTrace()V
    :try_end_3c
    .catchall {:try_start_39 .. :try_end_3c} :catchall_36

    .line 822
    .end local v5    # "e":Ljava/io/IOException;
    if-eqz v3, :cond_4c

    .line 824
    :try_start_3e
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_41
    .catch Ljava/io/IOException; {:try_start_3e .. :try_end_41} :catch_2c

    goto :goto_2b

    .line 815
    :catch_42
    move-exception v5

    .line 817
    .local v5, "e1":Ljava/io/FileNotFoundException;
    :try_start_43
    invoke-virtual {v5}, Ljava/io/FileNotFoundException;->printStackTrace()V
    :try_end_46
    .catchall {:try_start_43 .. :try_end_46} :catchall_36

    .line 822
    .end local v5    # "e1":Ljava/io/FileNotFoundException;
    if-eqz v3, :cond_4c

    .line 824
    :try_start_48
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_4b
    .catch Ljava/io/IOException; {:try_start_48 .. :try_end_4b} :catch_2c

    goto :goto_2b

    .line 832
    :cond_4c
    :goto_4c
    return-wide v0

    .line 822
    :goto_4d
    if-eqz v3, :cond_57

    .line 824
    :try_start_4f
    invoke-virtual {v3}, Ljava/io/BufferedInputStream;->close()V
    :try_end_52
    .catch Ljava/io/IOException; {:try_start_4f .. :try_end_52} :catch_53

    .line 828
    goto :goto_57

    .line 825
    :catch_53
    move-exception v6

    .line 827
    .local v6, "e":Ljava/io/IOException;
    invoke-virtual {v6}, Ljava/io/IOException;->printStackTrace()V

    .line 828
    .end local v6    # "e":Ljava/io/IOException;
    :cond_57
    :goto_57
    throw v5
.end method

.method public static getelffilearch(Ljava/lang/String;)Ljava/lang/String;
    .registers 4
    .param p0, "elffile"    # Ljava/lang/String;

    .line 123
    const/4 v0, 0x0

    .line 130
    .local v0, "file_arch":Ljava/lang/String;
    invoke-static {p0}, Lcom/wrapper/proxyapplication/Util;->readelfarch(Ljava/lang/String;)I

    move-result v1

    .line 132
    .local v1, "archcode":I
    const/4 v2, 0x3

    if-eq v1, v2, :cond_20

    const/16 v2, 0x28

    if-eq v1, v2, :cond_1d

    const/16 v2, 0x3e

    if-eq v1, v2, :cond_1a

    const/16 v2, 0xb7

    if-eq v1, v2, :cond_17

    .line 156
    const-string v0, "unknown"

    goto :goto_23

    .line 152
    :cond_17
    const-string v0, "arm64-v8a"

    .line 154
    goto :goto_23

    .line 147
    :cond_1a
    const-string v0, "86_64"

    .line 149
    goto :goto_23

    .line 141
    :cond_1d
    const-string v0, "armeabi"

    .line 143
    goto :goto_23

    .line 137
    :cond_20
    const-string v0, "86"

    .line 139
    nop

    .line 166
    :goto_23
    return-object v0
.end method

.method private static isFileValid(Ljava/lang/String;J)Z
    .registers 7
    .param p0, "path"    # Ljava/lang/String;
    .param p1, "length"    # J

    .line 591
    new-instance v0, Ljava/io/File;

    invoke-direct {v0, p0}, Ljava/io/File;-><init>(Ljava/lang/String;)V

    .line 592
    .local v0, "tmpfile":Ljava/io/File;
    invoke-virtual {v0}, Ljava/io/File;->exists()Z

    move-result v1

    if-eqz v1, :cond_15

    .line 593
    invoke-virtual {v0}, Ljava/io/File;->length()J

    move-result-wide v1

    cmp-long v3, v1, p1

    if-nez v3, :cond_15

    .line 594
    const/4 v1, 0x1

    return v1

    .line 597
    :cond_15
    const/4 v1, 0x0

    return v1
.end method

.method public static readelfarch(Ljava/lang/String;)I
    .registers 5
    .param p0, "filename"    # Ljava/lang/String;

    .line 93
    const/4 v0, 0x0

    .line 94
    .local v0, "c":I
    const/4 v1, 0x0

    .line 96
    .local v1, "rf":Ljava/io/RandomAccessFile;
    :try_start_2
    new-instance v2, Ljava/io/RandomAccessFile;

    const-string v3, "r"

    invoke-direct {v2, p0, v3}, Ljava/io/RandomAccessFile;-><init>(Ljava/lang/String;Ljava/lang/String;)V

    move-object v1, v2

    .line 97
    const-wide/16 v2, 0x12

    invoke-virtual {v1, v2, v3}, Ljava/io/RandomAccessFile;->seek(J)V

    .line 98
    invoke-virtual {v1}, Ljava/io/RandomAccessFile;->read()I

    move-result v2
    :try_end_13
    .catch Ljava/io/FileNotFoundException; {:try_start_2 .. :try_end_13} :catch_2a
    .catch Ljava/io/IOException; {:try_start_2 .. :try_end_13} :catch_20
    .catchall {:try_start_2 .. :try_end_13} :catchall_1e

    move v0, v2

    .line 108
    nop

    .line 109
    :try_start_15
    invoke-virtual {v1}, Ljava/io/RandomAccessFile;->close()V
    :try_end_18
    .catch Ljava/io/IOException; {:try_start_15 .. :try_end_18} :catch_19

    .line 114
    :cond_18
    :goto_18
    goto :goto_34

    .line 111
    :catch_19
    move-exception v2

    .line 112
    .local v2, "e":Ljava/io/IOException;
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V

    .line 115
    .end local v2    # "e":Ljava/io/IOException;
    goto :goto_34

    .line 107
    :catchall_1e
    move-exception v2

    goto :goto_35

    .line 103
    :catch_20
    move-exception v2

    .line 104
    .restart local v2    # "e":Ljava/io/IOException;
    :try_start_21
    invoke-virtual {v2}, Ljava/io/IOException;->printStackTrace()V
    :try_end_24
    .catchall {:try_start_21 .. :try_end_24} :catchall_1e

    .line 108
    .end local v2    # "e":Ljava/io/IOException;
    if-eqz v1, :cond_18

    .line 109
    :try_start_26
    invoke-virtual {v1}, Ljava/io/RandomAccessFile;->close()V
    :try_end_29
    .catch Ljava/io/IOException; {:try_start_26 .. :try_end_29} :catch_19

    goto :goto_18

    .line 100
    :catch_2a
    move-exception v2

    .line 101
    .local v2, "e":Ljava/io/FileNotFoundException;
    :try_start_2b
    invoke-virtual {v2}, Ljava/io/FileNotFoundException;->printStackTrace()V
    :try_end_2e
    .catchall {:try_start_2b .. :try_end_2e} :catchall_1e

    .line 108
    .end local v2    # "e":Ljava/io/FileNotFoundException;
    if-eqz v1, :cond_18

    .line 109
    :try_start_30
    invoke-virtual {v1}, Ljava/io/RandomAccessFile;->close()V
    :try_end_33
    .catch Ljava/io/IOException; {:try_start_30 .. :try_end_33} :catch_19

    goto :goto_18

    .line 117
    :goto_34
    return v0

    .line 108
    :goto_35
    if-eqz v1, :cond_40

    .line 109
    :try_start_37
    invoke-virtual {v1}, Ljava/io/RandomAccessFile;->close()V
    :try_end_3a
    .catch Ljava/io/IOException; {:try_start_37 .. :try_end_3a} :catch_3b

    goto :goto_40

    .line 111
    :catch_3b
    move-exception v3

    .line 112
    .local v3, "e":Ljava/io/IOException;
    invoke-virtual {v3}, Ljava/io/IOException;->printStackTrace()V

    goto :goto_41

    .line 114
    .end local v3    # "e":Ljava/io/IOException;
    :cond_40
    :goto_40
    nop

    :goto_41
    throw v2
.end method
